"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import {
  ArrowLeft,
  Mail,
  Phone,
  Calendar,
  MapPin,
  FileText,
  Star,
  Edit,
  Trash2,
  MessageSquare
} from "lucide-react";
import { Guest } from "@/features/guests/types";
import { GuestNote, CreateNoteData } from "@/features/guest-notes/types";
import { AddNoteModal } from "@/features/guest-notes/components/add-note-modal";
import { NoteCard } from "@/features/guest-notes/components/note-card";
import { Review, CreateReviewData } from "@/features/reviews/types";
import { AddReviewModal } from "@/features/reviews/components/add-review-modal";
import { ReviewCard } from "@/features/reviews/components/review-card";

export default function GuestProfilePage() {
  const params = useParams();
  const router = useRouter();
  const [guest, setGuest] = useState<Guest | null>(null);
  const [notes, setNotes] = useState<GuestNote[]>([]);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddNoteModal, setShowAddNoteModal] = useState(false);
  const [showAddReviewModal, setShowAddReviewModal] = useState(false);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch guest data
        const guestResponse = await fetch(`/api/guests/${params.id}`);
        if (!guestResponse.ok) {
          throw new Error('Failed to fetch guest');
        }
        const guestData = await guestResponse.json();
        setGuest(guestData);

        // Fetch notes
        const notesResponse = await fetch(`/api/guests/${params.id}/notes`);
        if (notesResponse.ok) {
          const notesData = await notesResponse.json();
          setNotes(notesData);
        }

        // Fetch reviews
        const reviewsResponse = await fetch(`/api/guests/${params.id}/reviews`);
        if (reviewsResponse.ok) {
          const reviewsData = await reviewsResponse.json();
          setReviews(reviewsData);
        }

        // Get current user ID (from auth context or API)
        const userResponse = await fetch('/api/test');
        if (userResponse.ok) {
          const userData = await userResponse.json();
          setCurrentUserId(userData.userId);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchData();
    }
  }, [params.id]);

  const handleAddNote = async (noteData: CreateNoteData) => {
    try {
      const response = await fetch(`/api/guests/${params.id}/notes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(noteData),
      });

      if (!response.ok) {
        throw new Error('Failed to add note');
      }

      const newNote = await response.json();
      setNotes(prev => [newNote, ...prev]);
    } catch (error) {
      console.error('Error adding note:', error);
      throw error;
    }
  };

  const handleAddReview = async (reviewData: CreateReviewData) => {
    try {
      let response: Response;

      if (reviewData.images && reviewData.images.length > 0) {
        // Send as FormData when images are present
        const formData = new FormData();

        // Add review data
        formData.append('rating', reviewData.rating.toString());
        if (reviewData.title) formData.append('title', reviewData.title);
        if (reviewData.comment) formData.append('comment', reviewData.comment);
        if (reviewData.stayDates) formData.append('stayDates', JSON.stringify(reviewData.stayDates));
        if (reviewData.propertyType) formData.append('propertyType', reviewData.propertyType);
        if (reviewData.wouldRecommend !== undefined) formData.append('wouldRecommend', reviewData.wouldRecommend.toString());
        if (reviewData.tags) formData.append('tags', JSON.stringify(reviewData.tags));

        // Add image files
        reviewData.images.forEach((file, index) => {
          formData.append(`image_${index}`, file);
        });

        response = await fetch(`/api/guests/${params.id}/reviews`, {
          method: 'POST',
          body: formData,
        });
      } else {
        // Send as JSON when no images
        const { images, ...dataWithoutImages } = reviewData;
        response = await fetch(`/api/guests/${params.id}/reviews`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(dataWithoutImages),
        });
      }

      if (!response.ok) {
        throw new Error('Failed to add review');
      }

      const newReview = await response.json();
      setReviews(prev => [newReview, ...prev]);

      // Refresh guest data to get updated rating
      const guestResponse = await fetch(`/api/guests/${params.id}`);
      if (guestResponse.ok) {
        const updatedGuest = await guestResponse.json();
        setGuest(updatedGuest);
      }
    } catch (error) {
      console.error('Error adding review:', error);
      throw error;
    }
  };

  const handleVerifyNote = async (noteId: string, comment: string, isConfirming: boolean) => {
    try {
      const response = await fetch(`/api/notes/${noteId}/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ comment, isConfirming }),
      });

      if (!response.ok) {
        throw new Error('Failed to verify note');
      }

      const verification = await response.json();

      // Update the notes state with the new verification
      setNotes(prev => prev.map(note => {
        if (note.id === noteId) {
          return {
            ...note,
            verifications: [...(note.verifications || []), verification],
            verificationCount: isConfirming ? note.verificationCount + 1 : note.verificationCount,
            isVerified: isConfirming && note.verificationCount + 1 >= 2,
          };
        }
        return note;
      }));
    } catch (error) {
      console.error('Error verifying note:', error);
      throw error;
    }
  };

  if (loading) {
    return (
      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !guest) {
    return (
      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-airbnb-hof mb-4">Guest Not Found</h2>
          <p className="text-airbnb-foggy mb-6">{error || 'The guest you are looking for does not exist.'}</p>
          <Button onClick={() => router.back()} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4 text-airbnb-foggy hover:text-airbnb-hof"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Guests
        </Button>

        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-3xl font-bold text-airbnb-hof">
              {guest.firstName} {guest.lastName}
            </h1>
            <div className="flex items-center mt-2 space-x-4">
              <div className="flex items-center">
                <Star className="h-5 w-5 text-yellow-400 fill-current" />
                <span className="ml-1 font-medium">
                  {Number(guest.averageRating || 0).toFixed(1)}
                </span>
                <span className="text-airbnb-foggy ml-1">
                  ({guest.totalReviews || 0} reviews)
                </span>
              </div>
              <Badge
                variant={(guest.totalReviews || 0) > 0 ? "default" : "secondary"}
                className={(guest.totalReviews || 0) > 0 ? "bg-airbnb-babu" : ""}
              >
                {(guest.totalReviews || 0) > 0 ? "Reviewed" : "New Guest"}
              </Badge>
            </div>
          </div>

          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-airbnb-hof">Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {guest.email && (
                <div className="flex items-center">
                  <Mail className="h-5 w-5 text-airbnb-foggy mr-3" />
                  <div>
                    <p className="font-medium">{guest.email}</p>
                    <p className="text-sm text-airbnb-foggy">Email</p>
                  </div>
                </div>
              )}

              {guest.phone && (
                <div className="flex items-center">
                  <Phone className="h-5 w-5 text-airbnb-foggy mr-3" />
                  <div>
                    <p className="font-medium">{guest.phone}</p>
                    <p className="text-sm text-airbnb-foggy">Phone</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Personal Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-airbnb-hof">Personal Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {guest.dateOfBirth && (
                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-airbnb-foggy mr-3" />
                  <div>
                    <p className="font-medium">
                      {new Date(guest.dateOfBirth).toLocaleDateString()}
                    </p>
                    <p className="text-sm text-airbnb-foggy">Date of Birth</p>
                  </div>
                </div>
              )}

              {guest.nationality && (
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 text-airbnb-foggy mr-3" />
                  <div>
                    <p className="font-medium">{guest.nationality}</p>
                    <p className="text-sm text-airbnb-foggy">Nationality</p>
                  </div>
                </div>
              )}

              {guest.documentType && guest.documentNumber && (
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-airbnb-foggy mr-3" />
                  <div>
                    <p className="font-medium">
                      {guest.documentType.toUpperCase()}: {guest.documentNumber}
                    </p>
                    <p className="text-sm text-airbnb-foggy">Document</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Emergency Contact */}
          {(guest.emergencyContact || guest.emergencyPhone) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-airbnb-hof">Emergency Contact</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {guest.emergencyContact && (
                  <div>
                    <p className="font-medium">{guest.emergencyContact}</p>
                    <p className="text-sm text-airbnb-foggy">Contact Name</p>
                  </div>
                )}

                {guest.emergencyPhone && (
                  <div>
                    <p className="font-medium">{guest.emergencyPhone}</p>
                    <p className="text-sm text-airbnb-foggy">Phone Number</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          {guest.notes && (
            <Card>
              <CardHeader>
                <CardTitle className="text-airbnb-hof">Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-airbnb-foggy whitespace-pre-wrap">{guest.notes}</p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-airbnb-hof">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                className="w-full bg-airbnb-rausch hover:bg-airbnb-rausch/90"
                onClick={() => setShowAddReviewModal(true)}
              >
                <Star className="h-4 w-4 mr-2" />
                Add Review
              </Button>
              <Button
                variant="outline"
                className="w-full border-airbnb-rausch text-airbnb-rausch hover:bg-airbnb-rausch hover:text-white"
                onClick={() => setShowAddNoteModal(true)}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                Add Note
              </Button>
              <Button variant="outline" className="w-full">
                <Calendar className="h-4 w-4 mr-2" />
                View Stays
              </Button>
              <Button variant="outline" className="w-full">
                <FileText className="h-4 w-4 mr-2" />
                Export Data
              </Button>
            </CardContent>
          </Card>

          {/* Guest Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-airbnb-hof">Guest Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-airbnb-hof">
                  {guest.totalReviews || 0}
                </div>
                <p className="text-sm text-airbnb-foggy">Total Reviews</p>
              </div>

              <div className="border-t border-gray-200 my-4"></div>

              <div className="text-center">
                <div className="text-2xl font-bold text-airbnb-hof">
                  {Number(guest.averageRating || 0).toFixed(1)}
                </div>
                <p className="text-sm text-airbnb-foggy">Average Rating</p>
              </div>

              <div className="border-t border-gray-200 my-4"></div>

              <div className="text-center">
                <div className="text-2xl font-bold text-airbnb-hof">
                  {new Date(guest.createdAt).toLocaleDateString()}
                </div>
                <p className="text-sm text-airbnb-foggy">Member Since</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Reviews Section */}
      <div className="mt-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-airbnb-hof">Guest Reviews ({reviews.length})</h2>
          <Button
            onClick={() => setShowAddReviewModal(true)}
            className="bg-airbnb-rausch hover:bg-airbnb-rausch/90"
          >
            <Star className="h-4 w-4 mr-2" />
            Add Review
          </Button>
        </div>

        {reviews.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <Star className="h-12 w-12 text-airbnb-foggy mx-auto mb-4" />
              <h3 className="text-lg font-medium text-airbnb-hof mb-2">No reviews yet</h3>
              <p className="text-airbnb-foggy mb-4">
                Be the first to review this guest and help the community.
              </p>
              <Button
                onClick={() => setShowAddReviewModal(true)}
                className="bg-airbnb-rausch hover:bg-airbnb-rausch/90"
              >
                Add First Review
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {reviews.map((review) => (
              <ReviewCard
                key={review.id}
                review={review}
              />
            ))}
          </div>
        )}
      </div>

      {/* Notes Section */}
      <div className="mt-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-airbnb-hof">Guest Notes & Verifications ({notes.length})</h2>
          <Button
            onClick={() => setShowAddNoteModal(true)}
            variant="outline"
            className="border-airbnb-rausch text-airbnb-rausch hover:bg-airbnb-rausch hover:text-white"
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            Add Note
          </Button>
        </div>

        {notes.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <MessageSquare className="h-12 w-12 text-airbnb-foggy mx-auto mb-4" />
              <h3 className="text-lg font-medium text-airbnb-hof mb-2">No notes yet</h3>
              <p className="text-airbnb-foggy mb-4">
                Be the first to share your experience with this guest.
              </p>
              <Button
                onClick={() => setShowAddNoteModal(true)}
                className="bg-airbnb-rausch hover:bg-airbnb-rausch/90"
              >
                Add First Note
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {notes.map((note) => (
              <NoteCard
                key={note.id}
                note={note}
                onVerify={handleVerifyNote}
                currentUserId={currentUserId || ''}
              />
            ))}
          </div>
        )}
      </div>

      {/* Add Review Modal */}
      <AddReviewModal
        isOpen={showAddReviewModal}
        onClose={() => setShowAddReviewModal(false)}
        onSubmit={handleAddReview}
        guestName={`${guest?.firstName} ${guest?.lastName}`}
      />

      {/* Add Note Modal */}
      <AddNoteModal
        isOpen={showAddNoteModal}
        onClose={() => setShowAddNoteModal(false)}
        onSubmit={handleAddNote}
        guestName={`${guest?.firstName} ${guest?.lastName}`}
      />
    </div>
  );
}
