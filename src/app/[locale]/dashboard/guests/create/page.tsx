"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { CreateGuestForm } from "@/features/guests/components/create-guest-form";
import { useToast } from "@/hooks/use-toast";

export default function CreateGuestPage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleSubmit = async (data: any) => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/guests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to create guest');
      }

      const guest = await response.json();
      
      toast({
        title: "Success!",
        description: "Guest created successfully.",
      });

      router.push('/dashboard/guests');
    } catch (error) {
      console.error('Error creating guest:', error);
      toast({
        title: "Error",
        description: "Failed to create guest. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-airbnb-hof">Add New Guest</h1>
        <p className="text-airbnb-foggy mt-2">
          Create a new guest profile for your directory
        </p>
      </div>

      <CreateGuestForm onSubmit={handleSubmit} isLoading={isLoading} />
    </div>
  );
}
