"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Search, Users, Star, Mail, Phone } from "lucide-react";
import { Guest } from "@/features/guests/types";

export default function GuestsPage() {
  const [guests, setGuests] = useState<Guest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    fetchGuests();
  }, [searchTerm]);

  const fetchGuests = async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);

      const response = await fetch(`/api/guests?${params}`);
      if (response.ok) {
        const data = await response.json();
        setGuests(data);
      }
    } catch (error) {
      console.error('Error fetching guests:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchGuests();
  };

  if (isLoading) {
    return (
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-airbnb-rausch mx-auto"></div>
          <p className="mt-4 text-airbnb-foggy">Loading guests...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-airbnb-hof">Guest Directory</h1>
          <p className="text-airbnb-foggy mt-2">
            Manage and search through your guest database
          </p>
        </div>
        <Link href="/dashboard/guests/create">
          <Button className="bg-airbnb-rausch hover:bg-airbnb-rausch/90">
            <Plus className="h-4 w-4 mr-2" />
            Add Guest
          </Button>
        </Link>
      </div>

      {/* Search */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-airbnb-foggy h-4 w-4" />
              <Input
                placeholder="Search guests by name or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button type="submit" variant="outline">
              Search
            </Button>
          </form>
        </CardContent>
      </Card>

      {guests.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <Users className="h-12 w-12 text-airbnb-foggy mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-airbnb-hof mb-2">
              {searchTerm ? "No guests found" : "No guests yet"}
            </h3>
            <p className="text-airbnb-foggy mb-6">
              {searchTerm
                ? "Try adjusting your search terms or add a new guest."
                : "Start building your guest directory by adding your first guest."
              }
            </p>
            <Link href="/dashboard/guests/create">
              <Button className="bg-airbnb-rausch hover:bg-airbnb-rausch/90">
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Guest
              </Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {guests.map((guest) => (
            <Card key={guest.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-airbnb-hof">
                      {guest.firstName} {guest.lastName}
                    </CardTitle>
                    <div className="flex items-center mt-2">
                      <div className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="ml-1 text-sm font-medium">
                          {Number(guest.averageRating || 0).toFixed(1)}
                        </span>
                      </div>
                      <span className="text-sm text-airbnb-foggy ml-2">
                        ({guest.totalReviews || 0} reviews)
                      </span>
                    </div>
                  </div>
                  <Badge
                    variant={(guest.totalReviews || 0) > 0 ? "default" : "secondary"}
                    className={(guest.totalReviews || 0) > 0 ? "bg-airbnb-babu" : ""}
                  >
                    {(guest.totalReviews || 0) > 0 ? "Reviewed" : "New"}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {guest.email && (
                    <div className="flex items-center text-sm text-airbnb-foggy">
                      <Mail className="h-4 w-4 mr-2" />
                      <span className="truncate">{guest.email}</span>
                    </div>
                  )}

                  {guest.phone && (
                    <div className="flex items-center text-sm text-airbnb-foggy">
                      <Phone className="h-4 w-4 mr-2" />
                      <span>{guest.phone}</span>
                    </div>
                  )}

                  {guest.nationality && (
                    <div className="text-sm text-airbnb-foggy">
                      <span className="font-medium">Nationality:</span> {guest.nationality}
                    </div>
                  )}

                  <div className="pt-4">
                    <Link href={`/dashboard/guests/${guest.id}`}>
                      <Button
                        variant="outline"
                        className="w-full border-airbnb-rausch text-airbnb-rausch hover:bg-airbnb-rausch hover:text-white"
                      >
                        View Profile
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
