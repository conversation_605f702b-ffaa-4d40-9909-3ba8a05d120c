"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Users, Star, Mail, Phone } from "lucide-react";
import { Guest } from "@/features/guests/types";
import { ViewToggle } from "@/shared/components/view-toggle";
import { DataTable } from "@/shared/components/data-table";
import { usePaginatedData } from "@/shared/hooks/use-paginated-data";
import { fetchGuests } from "@/features/guests/api/guests-api";
import { guestsTableColumns } from "@/features/guests/components/guests-table-columns";

export default function GuestsPage() {
  const [view, setView] = useState<'cards' | 'table'>('cards');

  const {
    data: guests,
    pagination,
    isLoading,
    params,
    updateParams,
  } = usePaginatedData(fetchGuests, {
    initialLimit: 10,
  });

  const handleViewChange = (newView: 'cards' | 'table') => {
    setView(newView);
  };

  const handleSearchChange = (search: string) => {
    updateParams({ search });
  };

  const handlePaginationChange = (newParams: any) => {
    updateParams(newParams);
  };

  if (isLoading && guests.length === 0) {
    return (
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-airbnb-rausch mx-auto"></div>
          <p className="mt-4 text-airbnb-foggy">Loading guests...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-airbnb-hof">Guest Directory</h1>
          <p className="text-airbnb-foggy mt-2">
            Manage and search through your guest database
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <ViewToggle view={view} onViewChange={handleViewChange} />
          <Link href="/dashboard/guests/create">
            <Button className="bg-airbnb-rausch hover:bg-airbnb-rausch/90">
              <Plus className="h-4 w-4 mr-2" />
              Add Guest
            </Button>
          </Link>
        </div>
      </div>

      {/* Content */}
      {view === 'table' ? (
        <DataTable
          data={guests}
          columns={guestsTableColumns}
          pagination={pagination}
          onPaginationChange={handlePaginationChange}
          isLoading={isLoading}
          searchPlaceholder="Search guests by name, email, or nationality..."
          searchValue={params.search || ""}
          onSearchChange={handleSearchChange}
        />
      ) : (

        <div className="space-y-6">
          {/* Search Bar for Cards View */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex-1 relative">
                <input
                  type="text"
                  placeholder="Search guests by name, email, or nationality..."
                  value={params.search || ""}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-airbnb-rausch focus:border-transparent"
                />
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                  <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </CardContent>
          </Card>

          {guests.length === 0 ? (
            <Card className="text-center py-12">
              <CardContent>
                <Users className="h-12 w-12 text-airbnb-foggy mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-airbnb-hof mb-2">
                  {params.search ? "No guests found" : "No guests yet"}
                </h3>
                <p className="text-airbnb-foggy mb-6">
                  {params.search
                    ? "Try adjusting your search terms or add a new guest."
                    : "Start building your guest directory by adding your first guest."
                  }
                </p>
                <Link href="/dashboard/guests/create">
                  <Button className="bg-airbnb-rausch hover:bg-airbnb-rausch/90">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Your First Guest
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {guests.map((guest) => (
                  <Card key={guest.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <CardTitle className="text-airbnb-hof">
                            {guest.firstName} {guest.lastName}
                          </CardTitle>
                          <div className="flex items-center mt-2">
                            <div className="flex items-center">
                              <Star className="h-4 w-4 text-yellow-400 fill-current" />
                              <span className="ml-1 text-sm font-medium">
                                {Number(guest.averageRating || 0).toFixed(1)}
                              </span>
                            </div>
                            <span className="text-sm text-airbnb-foggy ml-2">
                              ({guest.totalReviews || 0} reviews)
                            </span>
                          </div>
                        </div>
                        <Badge
                          variant={(guest.totalReviews || 0) > 0 ? "default" : "secondary"}
                          className={(guest.totalReviews || 0) > 0 ? "bg-airbnb-babu" : ""}
                        >
                          {(guest.totalReviews || 0) > 0 ? "Reviewed" : "New"}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {guest.email && (
                          <div className="flex items-center text-sm text-airbnb-foggy">
                            <Mail className="h-4 w-4 mr-2" />
                            <span className="truncate">{guest.email}</span>
                          </div>
                        )}

                        {guest.phone && (
                          <div className="flex items-center text-sm text-airbnb-foggy">
                            <Phone className="h-4 w-4 mr-2" />
                            <span>{guest.phone}</span>
                          </div>
                        )}

                        {guest.nationality && (
                          <div className="text-sm text-airbnb-foggy">
                            <span className="font-medium">Nationality:</span> {guest.nationality}
                          </div>
                        )}

                        <div className="pt-4">
                          <Link href={`/dashboard/guests/${guest.id}`}>
                            <Button
                              variant="outline"
                              className="w-full border-airbnb-rausch text-airbnb-rausch hover:bg-airbnb-rausch hover:text-white"
                            >
                              View Profile
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Pagination for Cards View */}
              <div className="mt-6">
                <div className="flex items-center justify-between px-2 py-4">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm text-airbnb-foggy">
                      Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} results
                    </p>
                    <select
                      value={pagination.limit}
                      onChange={(e) => handlePaginationChange({ limit: parseInt(e.target.value), page: 1 })}
                      className="h-8 w-[70px] border border-gray-300 rounded text-sm"
                    >
                      {[10, 25, 50, 100].map((pageSize) => (
                        <option key={pageSize} value={pageSize}>
                          {pageSize}
                        </option>
                      ))}
                    </select>
                    <p className="text-sm text-airbnb-foggy">per page</p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePaginationChange({ page: pagination.page - 1 })}
                      disabled={!pagination.hasPrev}
                    >
                      Previous
                    </Button>
                    <span className="text-sm text-airbnb-foggy">
                      Page {pagination.page} of {pagination.totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePaginationChange({ page: pagination.page + 1 })}
                      disabled={!pagination.hasNext}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
