"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Organization } from "@/features/organizations/types";
import { 
  Building2, 
  Users, 
  MapPin, 
  Phone, 
  Mail, 
  Globe, 
  Settings,
  UserPlus,
  Star,
  Calendar
} from "lucide-react";

export default function OrganizationDashboardPage() {
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();

  const organizationId = params.id as string;

  useEffect(() => {
    fetchOrganization();
  }, [organizationId]);

  const fetchOrganization = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/organizations/${organizationId}`);
      
      if (!response.ok) {
        if (response.status === 403) {
          toast({
            title: "Access Denied",
            description: "You don't have permission to view this organization.",
            variant: "destructive",
          });
          router.push('/dashboard/organizations');
          return;
        }
        throw new Error('Failed to fetch organization');
      }

      const org = await response.json();
      setOrganization(org);
    } catch (error) {
      console.error('Error fetching organization:', error);
      toast({
        title: "Error",
        description: "Failed to load organization details.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-airbnb-rausch mx-auto"></div>
          <p className="mt-4 text-airbnb-foggy">Loading organization...</p>
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 text-airbnb-foggy mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-airbnb-hof mb-2">Organization not found</h3>
          <p className="text-airbnb-foggy mb-6">
            The organization you're looking for doesn't exist or you don't have access to it.
          </p>
          <Link href="/dashboard/organizations">
            <Button className="bg-airbnb-rausch hover:bg-airbnb-rausch/90">
              Back to Organizations
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="flex justify-between items-start mb-8">
        <div className="flex items-center space-x-4">
          <div className="h-16 w-16 bg-airbnb-rausch/10 rounded-xl flex items-center justify-center">
            <Building2 className="h-8 w-8 text-airbnb-rausch" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-airbnb-hof">{organization.name}</h1>
            {organization.description && (
              <p className="text-airbnb-foggy mt-1">{organization.description}</p>
            )}
            <div className="flex items-center gap-4 mt-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                {organization.subscriptionStatus}
              </Badge>
              {organization.clerkOrganizationId && (
                <Badge variant="outline" className="text-blue-600 border-blue-200">
                  Clerk Integrated
                </Badge>
              )}
            </div>
          </div>
        </div>
        <div className="flex gap-2">
          <Link href={`/dashboard/organizations/${organization.id}/settings`}>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </Link>
        </div>
      </div>

      {/* Organization Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Organization Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {(organization.city || organization.state || organization.country) && (
              <div className="flex items-center text-sm">
                <MapPin className="h-4 w-4 mr-3 text-airbnb-foggy" />
                <span>
                  {[organization.address, organization.city, organization.state, organization.country]
                    .filter(Boolean)
                    .join(', ')}
                </span>
              </div>
            )}
            
            {organization.phone && (
              <div className="flex items-center text-sm">
                <Phone className="h-4 w-4 mr-3 text-airbnb-foggy" />
                <span>{organization.phone}</span>
              </div>
            )}
            
            {organization.email && (
              <div className="flex items-center text-sm">
                <Mail className="h-4 w-4 mr-3 text-airbnb-foggy" />
                <span>{organization.email}</span>
              </div>
            )}
            
            {organization.website && (
              <div className="flex items-center text-sm">
                <Globe className="h-4 w-4 mr-3 text-airbnb-foggy" />
                <a 
                  href={organization.website} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-airbnb-rausch hover:underline"
                >
                  {organization.website}
                </a>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Stats</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-2 text-airbnb-foggy" />
                <span className="text-sm">Max Users</span>
              </div>
              <span className="font-semibold">{organization.maxUsers}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-airbnb-foggy" />
                <span className="text-sm">Created</span>
              </div>
              <span className="font-semibold text-sm">
                {new Date(organization.createdAt).toLocaleDateString()}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <Users className="h-8 w-8 text-airbnb-rausch mx-auto mb-2" />
            <h3 className="font-semibold text-airbnb-hof">Manage Members</h3>
            <p className="text-sm text-airbnb-foggy mt-1">Add and manage team members</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <Star className="h-8 w-8 text-airbnb-rausch mx-auto mb-2" />
            <h3 className="font-semibold text-airbnb-hof">Guest Reviews</h3>
            <p className="text-sm text-airbnb-foggy mt-1">View and manage reviews</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <Building2 className="h-8 w-8 text-airbnb-rausch mx-auto mb-2" />
            <h3 className="font-semibold text-airbnb-hof">Properties</h3>
            <p className="text-sm text-airbnb-foggy mt-1">Manage your properties</p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <Settings className="h-8 w-8 text-airbnb-rausch mx-auto mb-2" />
            <h3 className="font-semibold text-airbnb-hof">Settings</h3>
            <p className="text-sm text-airbnb-foggy mt-1">Organization settings</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
