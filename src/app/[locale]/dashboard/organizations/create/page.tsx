"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { CreateOrganizationForm } from "@/features/organizations/components/create-organization-form";
import { useToast } from "@/hooks/use-toast";

export default function CreateOrganizationPage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  const handleSubmit = async (data: any) => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/organizations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to create organization');
      }

      const organization = await response.json();
      
      toast({
        title: "Success!",
        description: "Organization created successfully.",
      });

      router.push('/dashboard/organizations');
    } catch (error) {
      console.error('Error creating organization:', error);
      toast({
        title: "Error",
        description: "Failed to create organization. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-airbnb-hof">Create Organization</h1>
        <p className="text-airbnb-foggy mt-2">
          Set up your property management organization
        </p>
      </div>

      <CreateOrganizationForm onSubmit={handleSubmit} isLoading={isLoading} />
    </div>
  );
}
