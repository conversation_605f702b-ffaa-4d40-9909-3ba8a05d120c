"use client";

import { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Building2, Users, MapPin, Settings, ExternalLink } from "lucide-react";
import { Organization } from "@/features/organizations/types";
import { ViewToggle } from "@/shared/components/view-toggle";
import { DataTable } from "@/shared/components/data-table";
import { usePaginatedData } from "@/shared/hooks/use-paginated-data";
import { fetchOrganizations } from "@/features/organizations/api/organizations-api";
import { organizationsTableColumns } from "@/features/organizations/components/organizations-table-columns";

export default function OrganizationsPage() {
  const [view, setView] = useState<'cards' | 'table'>('cards');

  const {
    data: organizations,
    pagination,
    isLoading,
    params,
    updateParams,
  } = usePaginatedData(fetchOrganizations, {
    initialLimit: 10,
  });

  const handleViewChange = (newView: 'cards' | 'table') => {
    setView(newView);
  };

  const handleSearchChange = (search: string) => {
    updateParams({ search });
  };

  const handlePaginationChange = (newParams: any) => {
    updateParams(newParams);
  };

  if (isLoading && organizations.length === 0) {
    return (
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-airbnb-rausch mx-auto"></div>
          <p className="mt-4 text-airbnb-foggy">Loading organizations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-airbnb-hof">Organizations</h1>
          <p className="text-airbnb-foggy mt-2">
            Manage your property organizations and teams
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <ViewToggle view={view} onViewChange={handleViewChange} />
          <Link href="/dashboard/organizations/create">
            <Button className="bg-airbnb-rausch hover:bg-airbnb-rausch/90">
              <Plus className="h-4 w-4 mr-2" />
              Create Organization
            </Button>
          </Link>
        </div>
      </div>

      {/* Content */}
      {view === 'table' ? (
        <DataTable
          data={organizations}
          columns={organizationsTableColumns}
          pagination={pagination}
          onPaginationChange={handlePaginationChange}
          isLoading={isLoading}
          searchPlaceholder="Search organizations by name, description, or location..."
          searchValue={params.search || ""}
          onSearchChange={handleSearchChange}
        />
      ) : (
        <div className="space-y-6">
          {/* Search Bar for Cards View */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex-1 relative">
                <input
                  type="text"
                  placeholder="Search organizations by name, description, or location..."
                  value={params.search || ""}
                  onChange={(e) => handleSearchChange(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-airbnb-rausch focus:border-transparent"
                />
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                  <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </CardContent>
          </Card>

          {organizations.length === 0 ? (
            <Card className="text-center py-12">
              <CardContent>
                <Building2 className="h-12 w-12 text-airbnb-foggy mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-airbnb-hof mb-2">
                  {params.search ? "No organizations found" : "No organizations yet"}
                </h3>
                <p className="text-airbnb-foggy mb-6">
                  {params.search
                    ? "Try adjusting your search terms or create a new organization."
                    : "Create your first organization to start managing guests and reviews."
                  }
                </p>
                <Link href="/dashboard/organizations/create">
                  <Button className="bg-airbnb-rausch hover:bg-airbnb-rausch/90">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Organization
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {organizations.map((org) => (
                  <Card key={org.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="h-10 w-10 bg-airbnb-rausch/10 rounded-lg flex items-center justify-center">
                            <Building2 className="h-5 w-5 text-airbnb-rausch" />
                          </div>
                          <div>
                            <CardTitle className="text-airbnb-hof">{org.name}</CardTitle>
                            {org.description && (
                              <CardDescription className="mt-1">
                                {org.description}
                              </CardDescription>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {(org.city || org.state || org.country) && (
                          <div className="flex items-center text-sm text-airbnb-foggy">
                            <MapPin className="h-4 w-4 mr-2" />
                            <span>
                              {[org.city, org.state, org.country].filter(Boolean).join(', ')}
                            </span>
                          </div>
                        )}

                        <div className="pt-4 flex gap-2">
                          <Link href={`/dashboard/organizations/${org.id}`} className="flex-1">
                            <Button
                              variant="outline"
                              className="w-full border-airbnb-rausch text-airbnb-rausch hover:bg-airbnb-rausch hover:text-white"
                            >
                              <ExternalLink className="h-4 w-4 mr-2" />
                              View
                            </Button>
                          </Link>
                          <Link href={`/dashboard/organizations/${org.id}/settings`}>
                            <Button
                              variant="outline"
                              size="icon"
                              className="border-airbnb-foggy text-airbnb-foggy hover:bg-airbnb-foggy hover:text-white"
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Pagination for Cards View */}
              <div className="mt-6">
                <div className="flex items-center justify-between px-2 py-4">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm text-airbnb-foggy">
                      Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} results
                    </p>
                    <select
                      value={pagination.limit}
                      onChange={(e) => handlePaginationChange({ limit: parseInt(e.target.value), page: 1 })}
                      className="h-8 w-[70px] border border-gray-300 rounded text-sm"
                    >
                      {[10, 25, 50, 100].map((pageSize) => (
                        <option key={pageSize} value={pageSize}>
                          {pageSize}
                        </option>
                      ))}
                    </select>
                    <p className="text-sm text-airbnb-foggy">per page</p>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePaginationChange({ page: pagination.page - 1 })}
                      disabled={!pagination.hasPrev}
                    >
                      Previous
                    </Button>
                    <span className="text-sm text-airbnb-foggy">
                      Page {pagination.page} of {pagination.totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePaginationChange({ page: pagination.page + 1 })}
                      disabled={!pagination.hasNext}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
