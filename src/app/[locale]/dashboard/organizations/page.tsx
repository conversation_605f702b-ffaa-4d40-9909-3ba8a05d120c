"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Building2, Users, MapPin, Settings, ExternalLink } from "lucide-react";
import { Organization } from "@/features/organizations/types";

export default function OrganizationsPage() {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchOrganizations();
  }, []);

  const fetchOrganizations = async () => {
    try {
      const response = await fetch('/api/organizations');
      if (response.ok) {
        const data = await response.json();
        setOrganizations(data);
      }
    } catch (error) {
      console.error('Error fetching organizations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-airbnb-rausch mx-auto"></div>
          <p className="mt-4 text-airbnb-foggy">Loading organizations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-airbnb-hof">Organizations</h1>
          <p className="text-airbnb-foggy mt-2">
            Manage your property organizations and teams
          </p>
        </div>
        <Link href="/dashboard/organizations/create">
          <Button className="bg-airbnb-rausch hover:bg-airbnb-rausch/90">
            <Plus className="h-4 w-4 mr-2" />
            Create Organization
          </Button>
        </Link>
      </div>

      {organizations.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <Building2 className="h-12 w-12 text-airbnb-foggy mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-airbnb-hof mb-2">No organizations yet</h3>
            <p className="text-airbnb-foggy mb-6">
              Create your first organization to start managing guests and reviews.
            </p>
            <Link href="/dashboard/organizations/create">
              <Button className="bg-airbnb-rausch hover:bg-airbnb-rausch/90">
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Organization
              </Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {organizations.map((org) => (
            <Card key={org.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 bg-airbnb-rausch/10 rounded-lg flex items-center justify-center">
                      <Building2 className="h-5 w-5 text-airbnb-rausch" />
                    </div>
                    <div>
                      <CardTitle className="text-airbnb-hof">{org.name}</CardTitle>
                      {org.description && (
                        <CardDescription className="mt-1">
                          {org.description}
                        </CardDescription>
                      )}
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {(org.city || org.state || org.country) && (
                    <div className="flex items-center text-sm text-airbnb-foggy">
                      <MapPin className="h-4 w-4 mr-2" />
                      <span>
                        {[org.city, org.state, org.country].filter(Boolean).join(', ')}
                      </span>
                    </div>
                  )}

                  <div className="flex items-center text-sm text-airbnb-foggy">
                    <Users className="h-4 w-4 mr-2" />
                    <span>Max {org.maxUsers} users</span>
                  </div>

                  <div className="pt-4 flex gap-2">
                    <Link href={`/dashboard/organizations/${org.id}`} className="flex-1">
                      <Button
                        variant="outline"
                        className="w-full border-airbnb-rausch text-airbnb-rausch hover:bg-airbnb-rausch hover:text-white"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View
                      </Button>
                    </Link>
                    <Link href={`/dashboard/organizations/${org.id}/settings`}>
                      <Button
                        variant="outline"
                        size="icon"
                        className="border-airbnb-foggy text-airbnb-foggy hover:bg-airbnb-foggy hover:text-white"
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
