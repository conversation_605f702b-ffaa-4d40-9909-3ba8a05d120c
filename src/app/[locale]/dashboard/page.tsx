import { Card, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Users, Star, Building2, TrendingUp } from "lucide-react";

export default function DashboardPage() {
  return (
    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-airbnb-hof">Dashboard</h1>
        <p className="text-airbnb-foggy mt-2">
          Welcome to your guest management dashboard
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Guests</CardTitle>
            <Users className="h-4 w-4 text-airbnb-foggy" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-airbnb-hof">0</div>
            <p className="text-xs text-airbnb-foggy">
              +0% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reviews</CardTitle>
            <Star className="h-4 w-4 text-airbnb-foggy" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-airbnb-hof">0</div>
            <p className="text-xs text-airbnb-foggy">
              +0% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Organizations</CardTitle>
            <Building2 className="h-4 w-4 text-airbnb-foggy" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-airbnb-hof">0</div>
            <p className="text-xs text-airbnb-foggy">
              Active organizations
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Rating</CardTitle>
            <TrendingUp className="h-4 w-4 text-airbnb-foggy" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-airbnb-hof">0.0</div>
            <p className="text-xs text-airbnb-foggy">
              Overall guest rating
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-airbnb-hof">Quick Actions</CardTitle>
            <CardDescription>
              Get started with common tasks
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center py-8 text-airbnb-foggy">
              <p>Create your first organization to get started</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-airbnb-hof">Recent Activity</CardTitle>
            <CardDescription>
              Latest updates and reviews
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-airbnb-foggy">
              <p>No recent activity</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
