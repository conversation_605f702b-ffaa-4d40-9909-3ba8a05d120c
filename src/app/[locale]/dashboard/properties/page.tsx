"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Plus,
  Search,
  MapPin,
  Bed,
  Bath,
  Users,
  Square,
  Home,
  Settings,
  Eye,
  EyeOff
} from "lucide-react";
import Link from "next/link";
import { Property, CreatePropertyData } from "@/features/properties/types";
import { AddPropertyModal } from "@/features/properties/components/add-property-modal";
import { getPropertyTypeInfo } from "@/features/properties/constants/property-types";

export default function PropertiesPage() {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [showAddModal, setShowAddModal] = useState(false);
  const [currentOrganizationId, setCurrentOrganizationId] = useState<string | null>(null);
  const [hasOrganizations, setHasOrganizations] = useState(true);

  useEffect(() => {
    const fetchPropertiesData = async () => {
      try {
        // First, get user's organizations
        const orgsResponse = await fetch('/api/organizations');
        if (orgsResponse.ok) {
          const organizations = await orgsResponse.json();

          if (organizations.length === 0) {
            setHasOrganizations(false);
            setLoading(false);
            return;
          }

          // Use the first organization
          const orgId = organizations[0].id;
          setCurrentOrganizationId(orgId);

          // Fetch properties for this organization
          const propertiesResponse = await fetch(`/api/organizations/${orgId}/properties`);
          if (propertiesResponse.ok) {
            const data = await propertiesResponse.json();
            setProperties(data);
          }
        }
      } catch (error) {
        console.error('Error fetching properties:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPropertiesData();
  }, []);

  const handleAddProperty = async (propertyData: CreatePropertyData) => {
    if (!currentOrganizationId) return;

    try {
      const response = await fetch(`/api/organizations/${currentOrganizationId}/properties`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(propertyData),
      });

      if (!response.ok) {
        throw new Error('Failed to create property');
      }

      const newProperty = await response.json();
      setProperties(prev => [newProperty, ...prev]);
    } catch (error) {
      console.error('Error creating property:', error);
      throw error;
    }
  };

  const filteredProperties = properties.filter(property =>
    property.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    property.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
    property.type.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-64 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!hasOrganizations) {
    return (
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <Card>
          <CardContent className="text-center py-12">
            <Home className="h-12 w-12 text-airbnb-foggy mx-auto mb-4" />
            <h3 className="text-lg font-medium text-airbnb-hof mb-2">
              Create an organization first
            </h3>
            <p className="text-airbnb-foggy mb-6">
              You need to create an organization before you can add properties.
            </p>
            <Link href="/dashboard/organizations">
              <Button className="bg-airbnb-rausch hover:bg-airbnb-rausch/90">
                Create Organization
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-airbnb-hof">Properties</h1>
            <p className="text-airbnb-foggy mt-2">
              Manage your property listings and accommodations
            </p>
          </div>
          <Button
            onClick={() => setShowAddModal(true)}
            className="bg-airbnb-rausch hover:bg-airbnb-rausch/90"
          >
            <Plus className="h-5 w-5 mr-2" />
            Add Property
          </Button>
        </div>

        {/* Search */}
        <div className="mt-6 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-airbnb-foggy h-5 w-5" />
          <Input
            placeholder="Search properties by name, city, or type..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Properties Grid */}
      {filteredProperties.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <Home className="h-12 w-12 text-airbnb-foggy mx-auto mb-4" />
            <h3 className="text-lg font-medium text-airbnb-hof mb-2">
              {searchQuery ? 'No properties found' : 'No properties yet'}
            </h3>
            <p className="text-airbnb-foggy mb-6">
              {searchQuery
                ? 'Try adjusting your search terms.'
                : 'Get started by adding your first property listing.'
              }
            </p>
            {!searchQuery && (
              <Button
                onClick={() => setShowAddModal(true)}
                className="bg-airbnb-rausch hover:bg-airbnb-rausch/90"
              >
                <Plus className="h-5 w-5 mr-2" />
                Add Your First Property
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProperties.map((property) => {
            const typeInfo = getPropertyTypeInfo(property.type);

            return (
              <Card key={property.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl">{typeInfo.icon}</span>
                      <div>
                        <CardTitle className="text-lg text-airbnb-hof line-clamp-1">
                          {property.name}
                        </CardTitle>
                        <p className="text-sm text-airbnb-foggy">{typeInfo.label}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Badge
                        variant={property.isActive ? "default" : "secondary"}
                        className={property.isActive ? "bg-green-100 text-green-800" : ""}
                      >
                        {property.isActive ? "Active" : "Inactive"}
                      </Badge>
                      {property.isListed ? (
                        <Eye className="h-4 w-4 text-green-600" title="Listed" />
                      ) : (
                        <EyeOff className="h-4 w-4 text-gray-400" title="Not Listed" />
                      )}
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Location */}
                  <div className="flex items-center text-sm text-airbnb-foggy">
                    <MapPin className="h-4 w-4 mr-2" />
                    <span className="line-clamp-1">
                      {property.city}, {property.state && `${property.state}, `}{property.country}
                    </span>
                  </div>

                  {/* Property Details */}
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        <Bed className="h-4 w-4 mr-1 text-airbnb-foggy" />
                        <span>{property.bedrooms}</span>
                      </div>
                      <div className="flex items-center">
                        <Bath className="h-4 w-4 mr-1 text-airbnb-foggy" />
                        <span>{property.bathrooms}</span>
                      </div>
                      <div className="flex items-center">
                        <Users className="h-4 w-4 mr-1 text-airbnb-foggy" />
                        <span>{property.maxGuests}</span>
                      </div>
                    </div>
                  </div>

                  {/* Area */}
                  {property.area && (
                    <div className="flex items-center">
                      <Square className="h-4 w-4 mr-1 text-airbnb-foggy" />
                      <span className="text-sm text-airbnb-hof">
                        {property.area} {property.areaUnit === 'sqm' ? 'm²' : 'ft²'}
                      </span>
                    </div>
                  )}

                  {/* Description */}
                  {property.description && (
                    <p className="text-sm text-airbnb-foggy line-clamp-2">
                      {property.description}
                    </p>
                  )}

                  {/* Actions */}
                  <div className="flex space-x-2 pt-2">
                    <Link href={`/dashboard/properties/${property.id}`} className="flex-1">
                      <Button variant="outline" className="w-full">
                        <Settings className="h-4 w-4 mr-2" />
                        Manage
                      </Button>
                    </Link>
                    <Button
                      variant="outline"
                      className="border-airbnb-rausch text-airbnb-rausch hover:bg-airbnb-rausch hover:text-white"
                    >
                      View
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Add Property Modal */}
      <AddPropertyModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSubmit={handleAddProperty}
        organizationId={currentOrganizationId || ''}
      />
    </div>
  );
}
