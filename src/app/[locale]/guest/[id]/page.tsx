"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Star,
  User,
  Calendar,
  MapPin,
  Shield,
  MessageSquare,
  AlertTriangle
} from "lucide-react";
import { Guest } from "@/features/guests/types";
import { GuestNote } from "@/features/guest-notes/types";
import { Review } from "@/features/reviews/types";
import { ReviewCard } from "@/features/reviews/components/review-card";
import Link from "next/link";

export default function PublicGuestProfilePage() {
  const params = useParams();
  const router = useRouter();
  const [guest, setGuest] = useState<Guest | null>(null);
  const [notes, setNotes] = useState<GuestNote[]>([]);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch guest data (public endpoint)
        const guestResponse = await fetch(`/api/public/guests/${params.id}`);
        if (!guestResponse.ok) {
          throw new Error('Guest not found');
        }
        const guestData = await guestResponse.json();
        setGuest(guestData);

        // Fetch public notes
        const notesResponse = await fetch(`/api/public/guests/${params.id}/notes`);
        if (notesResponse.ok) {
          const notesData = await notesResponse.json();
          setNotes(notesData);
        }

        // Fetch public reviews
        const reviewsResponse = await fetch(`/api/public/guests/${params.id}/reviews`);
        if (reviewsResponse.ok) {
          const reviewsData = await reviewsResponse.json();
          setReviews(reviewsData);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchData();
    }
  }, [params.id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !guest) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <h2 className="text-2xl font-bold text-airbnb-hof mb-4">Guest Not Found</h2>
            <p className="text-airbnb-foggy mb-6">{error || 'The guest you are looking for does not exist.'}</p>
            <Link href="/search">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Search
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'cleanliness': return '🧹';
      case 'behavior': return '👤';
      case 'damage': return '⚠️';
      default: return '📝';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-6">
          <Link href="/search">
            <Button variant="ghost" className="mb-4 text-airbnb-foggy hover:text-airbnb-hof">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Search
            </Button>
          </Link>

          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <div className="h-16 w-16 bg-airbnb-babu rounded-full flex items-center justify-center">
                <User className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-airbnb-hof">
                  {guest.firstName} {guest.lastName}
                </h1>
                <div className="flex items-center mt-2 space-x-4">
                  <div className="flex items-center">
                    <Star className="h-5 w-5 text-yellow-400 fill-current" />
                    <span className="ml-1 font-medium">
                      {Number(guest.averageRating || 0).toFixed(1)}
                    </span>
                    <span className="text-airbnb-foggy ml-1">
                      ({guest.totalReviews || 0} reviews)
                    </span>
                  </div>
                  <Badge
                    variant={(guest.totalReviews || 0) > 0 ? "default" : "secondary"}
                    className={(guest.totalReviews || 0) > 0 ? "bg-airbnb-babu" : ""}
                  >
                    {(guest.totalReviews || 0) > 0 ? "Reviewed" : "New Guest"}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Info */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-airbnb-hof">Guest Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {guest.nationality && (
                  <div className="flex items-center">
                    <MapPin className="h-5 w-5 text-airbnb-foggy mr-3" />
                    <div>
                      <p className="font-medium">{guest.nationality}</p>
                      <p className="text-sm text-airbnb-foggy">Nationality</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center">
                  <Calendar className="h-5 w-5 text-airbnb-foggy mr-3" />
                  <div>
                    <p className="font-medium">
                      {new Date(guest.createdAt).toLocaleDateString()}
                    </p>
                    <p className="text-sm text-airbnb-foggy">Member Since</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Guest Reviews */}
            <Card>
              <CardHeader>
                <CardTitle className="text-airbnb-hof flex items-center">
                  <Star className="h-5 w-5 mr-2" />
                  Guest Reviews ({reviews.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {reviews.length === 0 ? (
                  <div className="text-center py-8">
                    <Star className="h-12 w-12 text-airbnb-foggy mx-auto mb-4" />
                    <p className="text-airbnb-foggy">No reviews yet.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {reviews.map((review) => (
                      <ReviewCard
                        key={review.id}
                        review={review}
                      />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Community Notes */}
            <Card>
              <CardHeader>
                <CardTitle className="text-airbnb-hof flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Community Notes ({notes.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {notes.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageSquare className="h-12 w-12 text-airbnb-foggy mx-auto mb-4" />
                    <p className="text-airbnb-foggy">No community notes yet.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {notes.map((note) => (
                      <div key={note.id} className="border rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <span className="text-lg">{getCategoryIcon(note.category)}</span>
                            <h4 className="font-semibold text-airbnb-hof">{note.title}</h4>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge className={getSeverityColor(note.severity)}>
                              {note.severity.toUpperCase()}
                            </Badge>
                            {note.isVerified && (
                              <Badge className="bg-green-100 text-green-800 border-green-200">
                                <Shield className="h-3 w-3 mr-1" />
                                Verified
                              </Badge>
                            )}
                          </div>
                        </div>

                        <p className="text-airbnb-foggy mb-3">{note.content}</p>

                        <div className="flex items-center justify-between text-sm text-airbnb-foggy">
                          <div className="flex items-center space-x-4">
                            <span>By {note.author?.firstName} {note.author?.lastName}</span>
                            <span>{new Date(note.createdAt).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span>{note.verificationCount} confirmations</span>
                            {note.verifications && note.verifications.length > 0 && (
                              <span>• {note.verifications.length} comments</span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Guest Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-airbnb-hof">Guest Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-airbnb-hof">
                    {guest.totalReviews || 0}
                  </div>
                  <p className="text-sm text-airbnb-foggy">Total Reviews</p>
                </div>

                <div className="border-t border-gray-200 my-4"></div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-airbnb-hof">
                    {Number(guest.averageRating || 0).toFixed(1)}
                  </div>
                  <p className="text-sm text-airbnb-foggy">Average Rating</p>
                </div>

                <div className="border-t border-gray-200 my-4"></div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-airbnb-hof">
                    {reviews.length}
                  </div>
                  <p className="text-sm text-airbnb-foggy">Reviews</p>
                </div>

                <div className="border-t border-gray-200 my-4"></div>

                <div className="text-center">
                  <div className="text-2xl font-bold text-airbnb-hof">
                    {notes.length}
                  </div>
                  <p className="text-sm text-airbnb-foggy">Community Notes</p>
                </div>
              </CardContent>
            </Card>

            {/* Call to Action */}
            <Card>
              <CardHeader>
                <CardTitle className="text-airbnb-hof">Are you a host?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-airbnb-foggy mb-4">
                  Sign in to add your own notes and help the community make informed decisions.
                </p>
                <Link href="/sign-in">
                  <Button className="w-full bg-airbnb-rausch hover:bg-airbnb-rausch/90">
                    Sign In to Contribute
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
