import Link from "next/link";
import { getTranslations } from "next-intl/server";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Building2, Users, Star, Shield, Zap, Globe } from "lucide-react";

export default async function Home({ params }: { params: { locale: string } }) {
  const t = await getTranslations("landing");
  const tNav = await getTranslations("navigation");
  const { locale } = params;
  return (
    <div className="min-h-screen bg-gradient-to-br from-airbnb-light to-white">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white/80 backdrop-blur-sm">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex h-16 justify-between items-center">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-airbnb-rausch rounded-lg flex items-center justify-center">
                <Building2 className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-airbnb-hof">GuestFile</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link href={`/${locale}/search`}>
                <Button variant="ghost" className="text-airbnb-hof hover:text-airbnb-rausch">
                  Search Guests
                </Button>
              </Link>
              <Link href={`/${locale}/sign-in`}>
                <Button variant="ghost" className="text-airbnb-hof hover:text-airbnb-rausch">
                  {tNav("signIn")}
                </Button>
              </Link>
              <Link href={`/${locale}/sign-up`}>
                <Button className="bg-airbnb-rausch hover:bg-airbnb-rausch/90">
                  {t("getStarted")}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl text-center">
          <h1 className="text-4xl sm:text-6xl font-bold text-airbnb-hof mb-6">
            {t("title")}
          </h1>
          <p className="text-xl text-airbnb-foggy mb-8 max-w-3xl mx-auto">
            {t("subtitle")}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href={`/${locale}/sign-up`}>
              <Button size="lg" className="bg-airbnb-rausch hover:bg-airbnb-rausch/90 text-lg px-8 py-3">
                {t("pricing.startTrial")}
              </Button>
            </Link>
            <Link href="#features">
              <Button size="lg" variant="outline" className="text-lg px-8 py-3 border-airbnb-rausch text-airbnb-rausch hover:bg-airbnb-rausch hover:text-white">
                {t("learnMore")}
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="mx-auto max-w-7xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-airbnb-hof mb-4">
              {t("features.title")}
            </h2>
            <p className="text-xl text-airbnb-foggy max-w-2xl mx-auto">
              {t("features.subtitle")}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="h-12 w-12 bg-airbnb-rausch/10 rounded-lg flex items-center justify-center mb-4">
                  <Users className="h-6 w-6 text-airbnb-rausch" />
                </div>
                <CardTitle className="text-airbnb-hof">{t("features.guestDirectory.title")}</CardTitle>
                <CardDescription>
                  {t("features.guestDirectory.description")}
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="h-12 w-12 bg-airbnb-babu/10 rounded-lg flex items-center justify-center mb-4">
                  <Star className="h-6 w-6 text-airbnb-babu" />
                </div>
                <CardTitle className="text-airbnb-hof">{t("features.reviewSystem.title")}</CardTitle>
                <CardDescription>
                  {t("features.reviewSystem.description")}
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="h-12 w-12 bg-airbnb-arches/10 rounded-lg flex items-center justify-center mb-4">
                  <Building2 className="h-6 w-6 text-airbnb-arches" />
                </div>
                <CardTitle className="text-airbnb-hof">{t("features.organizationManagement.title")}</CardTitle>
                <CardDescription>
                  {t("features.organizationManagement.description")}
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="h-12 w-12 bg-airbnb-rausch/10 rounded-lg flex items-center justify-center mb-4">
                  <Shield className="h-6 w-6 text-airbnb-rausch" />
                </div>
                <CardTitle className="text-airbnb-hof">{t("features.reputationScores.title")}</CardTitle>
                <CardDescription>
                  {t("features.reputationScores.description")}
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="h-12 w-12 bg-airbnb-babu/10 rounded-lg flex items-center justify-center mb-4">
                  <Zap className="h-6 w-6 text-airbnb-babu" />
                </div>
                <CardTitle className="text-airbnb-hof">{t("features.quickSearch.title")}</CardTitle>
                <CardDescription>
                  {t("features.quickSearch.description")}
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <div className="h-12 w-12 bg-airbnb-arches/10 rounded-lg flex items-center justify-center mb-4">
                  <Globe className="h-6 w-6 text-airbnb-arches" />
                </div>
                <CardTitle className="text-airbnb-hof">{t("features.globalNetwork.title")}</CardTitle>
                <CardDescription>
                  {t("features.globalNetwork.description")}
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 py-12 px-4 sm:px-6 lg:px-8">
        <div className="mx-auto max-w-7xl">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="h-8 w-8 bg-airbnb-rausch rounded-lg flex items-center justify-center">
                <Building2 className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-airbnb-hof">GuestFile</span>
            </div>
            <div className="text-airbnb-foggy">
              © 2024 GuestFile. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
