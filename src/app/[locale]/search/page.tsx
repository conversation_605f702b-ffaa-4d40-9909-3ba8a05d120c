"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, Star, User, Calendar, MapPin } from "lucide-react";
import Link from "next/link";

interface SearchResult {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  nationality?: string;
  averageRating: number | string | null;
  totalReviews: number | null;
  createdAt: string;
  notesCount?: number;
}

export default function GuestSearchPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    setHasSearched(true);

    try {
      const response = await fetch(`/api/search/guests?q=${encodeURIComponent(searchQuery)}`);
      if (response.ok) {
        const results = await response.json();
        setSearchResults(results);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-airbnb-hof mb-2">
              Guest Directory Search
            </h1>
            <p className="text-airbnb-foggy">
              Search for guest profiles and reviews from the community
            </p>
          </div>
        </div>
      </div>

      <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Form */}
        <Card className="mb-8">
          <CardContent className="pt-6">
            <form onSubmit={handleSearch} className="flex gap-4">
              <div className="flex-1">
                <Input
                  type="text"
                  placeholder="Search by name, email, or nationality..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="text-lg"
                />
              </div>
              <Button
                type="submit"
                disabled={isSearching || !searchQuery.trim()}
                className="bg-airbnb-rausch hover:bg-airbnb-rausch/90 px-8"
              >
                <Search className="h-5 w-5 mr-2" />
                {isSearching ? "Searching..." : "Search"}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Search Results */}
        {hasSearched && (
          <div>
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-airbnb-hof">
                {searchResults.length === 0 
                  ? "No guests found" 
                  : `${searchResults.length} guest${searchResults.length === 1 ? '' : 's'} found`
                }
              </h2>
              {searchResults.length === 0 && (
                <p className="text-airbnb-foggy mt-2">
                  Try searching with different keywords or check the spelling.
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {searchResults.map((guest) => (
                <Card key={guest.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="h-12 w-12 bg-airbnb-babu rounded-full flex items-center justify-center">
                          <User className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-airbnb-hof">
                            {guest.firstName} {guest.lastName}
                          </h3>
                          <p className="text-sm text-airbnb-foggy">{guest.email}</p>
                        </div>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-3">
                    {guest.nationality && (
                      <div className="flex items-center text-sm text-airbnb-foggy">
                        <MapPin className="h-4 w-4 mr-2" />
                        {guest.nationality}
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="ml-1 text-sm font-medium">
                          {Number(guest.averageRating || 0).toFixed(1)}
                        </span>
                        <span className="text-sm text-airbnb-foggy ml-1">
                          ({guest.totalReviews || 0} reviews)
                        </span>
                      </div>
                      
                      <Badge 
                        variant={(guest.totalReviews || 0) > 0 ? "default" : "secondary"}
                        className={(guest.totalReviews || 0) > 0 ? "bg-airbnb-babu" : ""}
                      >
                        {(guest.totalReviews || 0) > 0 ? "Reviewed" : "New"}
                      </Badge>
                    </div>

                    {guest.notesCount && guest.notesCount > 0 && (
                      <div className="text-sm text-airbnb-foggy">
                        {guest.notesCount} community note{guest.notesCount === 1 ? '' : 's'}
                      </div>
                    )}

                    <div className="flex items-center text-sm text-airbnb-foggy">
                      <Calendar className="h-4 w-4 mr-2" />
                      Member since {new Date(guest.createdAt).toLocaleDateString()}
                    </div>

                    <div className="pt-3">
                      <Link href={`/guest/${guest.id}`}>
                        <Button
                          variant="outline"
                          className="w-full border-airbnb-rausch text-airbnb-rausch hover:bg-airbnb-rausch hover:text-white"
                        >
                          View Profile
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Call to Action */}
        {!hasSearched && (
          <div className="text-center py-12">
            <div className="mx-auto max-w-md">
              <Search className="h-16 w-16 text-airbnb-foggy mx-auto mb-4" />
              <h3 className="text-lg font-medium text-airbnb-hof mb-2">
                Search Guest Profiles
              </h3>
              <p className="text-airbnb-foggy mb-6">
                Find guest profiles, read community reviews, and make informed decisions for your property.
              </p>
              <div className="text-sm text-airbnb-foggy">
                <p>• View verified guest reviews</p>
                <p>• Check community notes and verifications</p>
                <p>• Make informed hosting decisions</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
