import { NextRequest, NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';
import { db } from '@/db';
import { users, guests, reviews, properties, organizations, organizationMembers } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { getOrCreateUser } from '@/lib/auth-utils';

export async function DELETE(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await getOrCreateUser(userId);

    // Start transaction to ensure data consistency
    await db.transaction(async (tx) => {
      // Get user's organizations
      const userOrganizations = await tx.query.organizationMembers.findMany({
        where: eq(organizationMembers.userId, user.id),
      });

      const organizationIds = userOrganizations.map(om => om.organizationId);

      // Delete user's reviews first (due to foreign key constraints)
      await tx.delete(reviews).where(eq(reviews.reviewerId, user.id));

      // For each organization the user is a member of
      for (const orgId of organizationIds) {
        // Check if user is the only member of the organization
        const orgMembers = await tx.query.organizationMembers.findMany({
          where: eq(organizationMembers.organizationId, orgId),
        });

        if (orgMembers.length === 1 && orgMembers[0].userId === user.id) {
          // User is the only member, delete the entire organization and its data
          
          // Delete all guests in this organization
          await tx.delete(guests).where(eq(guests.organizationId, orgId));
          
          // Delete all properties in this organization
          await tx.delete(properties).where(eq(properties.organizationId, orgId));
          
          // Delete all reviews for guests in this organization
          await tx.delete(reviews).where(eq(reviews.organizationId, orgId));
          
          // Delete organization membership
          await tx.delete(organizationMembers).where(eq(organizationMembers.organizationId, orgId));
          
          // Delete the organization itself
          await tx.delete(organizations).where(eq(organizations.id, orgId));
        } else {
          // Other members exist, just remove this user's membership
          await tx.delete(organizationMembers).where(
            eq(organizationMembers.userId, user.id) && eq(organizationMembers.organizationId, orgId)
          );
        }
      }

      // Finally, delete the user record
      await tx.delete(users).where(eq(users.id, user.id));
    });

    // Delete user from Clerk
    try {
      await clerkClient.users.deleteUser(userId);
    } catch (clerkError) {
      console.error('Error deleting user from Clerk:', clerkError);
      // Continue even if Clerk deletion fails - the database cleanup is more important
    }

    // Log the deletion for audit purposes
    console.log(`User account deleted: ${userId} at ${new Date().toISOString()}`);

    return NextResponse.json({ 
      message: 'Account deleted successfully',
      deletedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error deleting user account:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
