import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db';
import { users, guests, reviews, properties, organizations, organizationMembers } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { getOrCreateUser } from '@/lib/auth-utils';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await getOrCreateUser(userId);

    // Collect all user data for export
    const exportData = {
      exportDate: new Date().toISOString(),
      user: {
        id: user.id,
        clerkId: user.clerkId,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        imageUrl: user.imageUrl,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
      organizations: [],
      guests: [],
      reviews: [],
      properties: [],
    };

    // Get user's organizations
    const userOrganizations = await db.query.organizationMembers.findMany({
      where: eq(organizationMembers.userId, user.id),
      with: {
        organization: true,
      },
    });

    exportData.organizations = userOrganizations.map(om => ({
      id: om.organization.id,
      name: om.organization.name,
      description: om.organization.description,
      role: om.role,
      joinedAt: om.createdAt,
    }));

    // Get all guests from user's organizations
    const organizationIds = userOrganizations.map(om => om.organizationId);
    
    if (organizationIds.length > 0) {
      // Get guests
      const userGuests = await db.query.guests.findMany({
        where: organizationIds.length === 1 
          ? eq(guests.organizationId, organizationIds[0])
          : undefined, // TODO: Handle multiple organizations with IN clause
      });

      exportData.guests = userGuests.map(guest => ({
        id: guest.id,
        organizationId: guest.organizationId,
        firstName: guest.firstName,
        lastName: guest.lastName,
        email: guest.email,
        phone: guest.phone,
        dateOfBirth: guest.dateOfBirth,
        nationality: guest.nationality,
        documentType: guest.documentType,
        documentNumber: guest.documentNumber,
        emergencyContact: guest.emergencyContact,
        emergencyPhone: guest.emergencyPhone,
        notes: guest.notes,
        imageUrl: guest.imageUrl,
        averageRating: guest.averageRating,
        totalReviews: guest.totalReviews,
        createdAt: guest.createdAt,
        updatedAt: guest.updatedAt,
      }));

      // Get reviews created by the user
      const userReviews = await db.query.reviews.findMany({
        where: eq(reviews.reviewerId, user.id),
      });

      exportData.reviews = userReviews.map(review => ({
        id: review.id,
        guestId: review.guestId,
        organizationId: review.organizationId,
        rating: review.rating,
        title: review.title,
        comment: review.comment,
        stayDates: review.stayDates,
        propertyType: review.propertyType,
        wouldRecommend: review.wouldRecommend,
        tags: review.tags,
        images: review.images,
        createdAt: review.createdAt,
        updatedAt: review.updatedAt,
      }));

      // Get properties from user's organizations
      const userProperties = await db.query.properties.findMany({
        where: organizationIds.length === 1 
          ? eq(properties.organizationId, organizationIds[0])
          : undefined, // TODO: Handle multiple organizations with IN clause
      });

      exportData.properties = userProperties.map(property => ({
        id: property.id,
        organizationId: property.organizationId,
        name: property.name,
        type: property.type,
        category: property.category,
        description: property.description,
        address: property.address,
        city: property.city,
        state: property.state,
        country: property.country,
        postalCode: property.postalCode,
        latitude: property.latitude,
        longitude: property.longitude,
        bedrooms: property.bedrooms,
        bathrooms: property.bathrooms,
        maxGuests: property.maxGuests,
        area: property.area,
        areaUnit: property.areaUnit,
        amenities: property.amenities,
        images: property.images,
        isActive: property.isActive,
        isListed: property.isListed,
        createdAt: property.createdAt,
        updatedAt: property.updatedAt,
      }));
    }

    // Add metadata about the export
    const metadata = {
      exportVersion: '1.0',
      totalRecords: {
        organizations: exportData.organizations.length,
        guests: exportData.guests.length,
        reviews: exportData.reviews.length,
        properties: exportData.properties.length,
      },
      dataProtectionCompliance: {
        lgpd: true,
        gdpr: true,
        exportReason: 'User requested data export under data protection rights',
      },
      instructions: {
        description: 'This file contains all your personal data stored in GuestFile',
        format: 'JSON',
        encoding: 'UTF-8',
        contact: 'For questions about this export, contact <EMAIL>',
      },
    };

    const finalExportData = {
      metadata,
      data: exportData,
    };

    // Create JSON response
    const jsonString = JSON.stringify(finalExportData, null, 2);
    const buffer = Buffer.from(jsonString, 'utf-8');

    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="guestfile-data-export-${new Date().toISOString().split('T')[0]}.json"`,
        'Content-Length': buffer.length.toString(),
      },
    });
  } catch (error) {
    console.error('Error exporting user data:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
