import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db';
import { guestNotes, users } from '@/db/schema';
import { eq, desc } from 'drizzle-orm';
import { getOrCreateUser } from '@/lib/auth-utils';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    try {
      await getOrCreateUser(userId);
    } catch (error) {
      console.error('Error getting/creating user:', error);
      return NextResponse.json({ error: 'Failed to authenticate user' }, { status: 500 });
    }

    // Get notes for the guest with author information
    const notes = await db
      .select({
        id: guestNotes.id,
        guestId: guestNotes.guestId,
        authorId: guestNotes.authorId,
        organizationId: guestNotes.organizationId,
        title: guestNotes.title,
        content: guestNotes.content,
        category: guestNotes.category,
        severity: guestNotes.severity,
        isVerified: guestNotes.isVerified,
        verificationCount: guestNotes.verificationCount,
        createdAt: guestNotes.createdAt,
        updatedAt: guestNotes.updatedAt,
        author: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
          email: users.email,
          imageUrl: users.imageUrl,
        },
      })
      .from(guestNotes)
      .leftJoin(users, eq(guestNotes.authorId, users.id))
      .where(eq(guestNotes.guestId, params.id))
      .orderBy(desc(guestNotes.createdAt));

    return NextResponse.json(notes);
  } catch (error) {
    console.error('Error fetching guest notes:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    const user = await getOrCreateUser(userId);

    const body = await request.json();
    const { title, content, category, severity } = body;

    if (!title || !content) {
      return NextResponse.json({ error: 'Title and content are required' }, { status: 400 });
    }

    // For now, we'll use the first organization the user belongs to
    // In a real app, you'd want to specify which organization context this note belongs to
    const organizationId = '00000000-0000-0000-0000-000000000000'; // Placeholder

    // Create the note
    const [newNote] = await db
      .insert(guestNotes)
      .values({
        guestId: params.id,
        authorId: user.id,
        organizationId,
        title,
        content,
        category: category || 'general',
        severity: severity || 'low',
      })
      .returning();

    // Return the note with author information
    const noteWithAuthor = await db
      .select({
        id: guestNotes.id,
        guestId: guestNotes.guestId,
        authorId: guestNotes.authorId,
        organizationId: guestNotes.organizationId,
        title: guestNotes.title,
        content: guestNotes.content,
        category: guestNotes.category,
        severity: guestNotes.severity,
        isVerified: guestNotes.isVerified,
        verificationCount: guestNotes.verificationCount,
        createdAt: guestNotes.createdAt,
        updatedAt: guestNotes.updatedAt,
        author: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
          email: users.email,
          imageUrl: users.imageUrl,
        },
      })
      .from(guestNotes)
      .leftJoin(users, eq(guestNotes.authorId, users.id))
      .where(eq(guestNotes.id, newNote.id))
      .limit(1);

    return NextResponse.json(noteWithAuthor[0]);
  } catch (error) {
    console.error('Error creating guest note:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
