import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db';
import { reviews, users, guests } from '@/db/schema';
import { eq, desc, avg, count } from 'drizzle-orm';
import { getOrCreateUser } from '@/lib/auth-utils';
import { uploadReviewImages } from '@/lib/s3-service';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    try {
      await getOrCreateUser(userId);
    } catch (error) {
      console.error('Error getting/creating user:', error);
      return NextResponse.json({ error: 'Failed to authenticate user' }, { status: 500 });
    }

    // Get reviews for the guest with reviewer information
    const guestReviews = await db
      .select({
        id: reviews.id,
        guestId: reviews.guestId,
        organizationId: reviews.organizationId,
        reviewerId: reviews.reviewerId,
        rating: reviews.rating,
        title: reviews.title,
        comment: reviews.comment,
        stayDates: reviews.stayDates,
        propertyType: reviews.propertyType,
        wouldRecommend: reviews.wouldRecommend,
        tags: reviews.tags,
        createdAt: reviews.createdAt,
        updatedAt: reviews.updatedAt,
        reviewer: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
          email: users.email,
          imageUrl: users.imageUrl,
        },
      })
      .from(reviews)
      .leftJoin(users, eq(reviews.reviewerId, users.id))
      .where(eq(reviews.guestId, params.id))
      .orderBy(desc(reviews.createdAt));

    return NextResponse.json(guestReviews);
  } catch (error) {
    console.error('Error fetching guest reviews:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    const user = await getOrCreateUser(userId);

    // Handle both JSON and FormData
    let reviewData: any;
    let imageFiles: File[] = [];

    const contentType = request.headers.get('content-type');

    if (contentType?.includes('multipart/form-data')) {
      // Handle FormData (with images)
      const formData = await request.formData();

      reviewData = {
        rating: parseInt(formData.get('rating') as string),
        title: formData.get('title') as string || undefined,
        comment: formData.get('comment') as string || undefined,
        stayDates: formData.get('stayDates') ? JSON.parse(formData.get('stayDates') as string) : undefined,
        propertyType: formData.get('propertyType') as string || undefined,
        wouldRecommend: formData.get('wouldRecommend') === 'true',
        tags: formData.get('tags') ? JSON.parse(formData.get('tags') as string) : [],
      };

      // Extract image files
      const imageEntries = Array.from(formData.entries()).filter(([key]) => key.startsWith('image_'));
      imageFiles = imageEntries.map(([, value]) => value as File);
    } else {
      // Handle JSON (no images)
      const body = await request.json();
      reviewData = body;
    }

    const {
      rating,
      title,
      comment,
      stayDates,
      propertyType,
      wouldRecommend,
      tags
    } = reviewData;

    if (!rating || rating < 1 || rating > 5) {
      return NextResponse.json({ error: 'Rating must be between 1 and 5' }, { status: 400 });
    }

    // For now, we'll use the first organization the user belongs to
    // In a real app, you'd want to specify which organization context this review belongs to
    const organizationId = '00000000-0000-0000-0000-000000000000'; // Placeholder

    // Upload images if any
    let uploadedImages: any[] = [];
    if (imageFiles.length > 0) {
      try {
        const uploadResults = await uploadReviewImages(imageFiles, `temp-${Date.now()}`);
        uploadedImages = uploadResults.map(result => ({
          url: result.url,
          key: result.key,
          caption: undefined
        }));
      } catch (error) {
        console.error('Error uploading images:', error);
        // Continue without images rather than failing the entire review
      }
    }

    // Create the review
    const [newReview] = await db
      .insert(reviews)
      .values({
        guestId: params.id,
        reviewerId: user.id,
        organizationId,
        rating,
        title,
        comment,
        stayDates,
        propertyType,
        wouldRecommend,
        tags,
        images: uploadedImages.length > 0 ? uploadedImages : null,
      })
      .returning();

    // Update guest's average rating and total reviews
    const reviewStats = await db
      .select({
        avgRating: avg(reviews.rating),
        totalReviews: count(reviews.id),
      })
      .from(reviews)
      .where(eq(reviews.guestId, params.id));

    if (reviewStats[0]) {
      await db
        .update(guests)
        .set({
          averageRating: reviewStats[0].avgRating?.toString() || '0',
          totalReviews: reviewStats[0].totalReviews || 0,
          updatedAt: new Date(),
        })
        .where(eq(guests.id, params.id));
    }

    // Return the review with reviewer information
    const reviewWithReviewer = await db
      .select({
        id: reviews.id,
        guestId: reviews.guestId,
        organizationId: reviews.organizationId,
        reviewerId: reviews.reviewerId,
        rating: reviews.rating,
        title: reviews.title,
        comment: reviews.comment,
        stayDates: reviews.stayDates,
        propertyType: reviews.propertyType,
        wouldRecommend: reviews.wouldRecommend,
        tags: reviews.tags,
        createdAt: reviews.createdAt,
        updatedAt: reviews.updatedAt,
        reviewer: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
          email: users.email,
          imageUrl: users.imageUrl,
        },
      })
      .from(reviews)
      .leftJoin(users, eq(reviews.reviewerId, users.id))
      .where(eq(reviews.id, newReview.id))
      .limit(1);

    return NextResponse.json(reviewWithReviewer[0]);
  } catch (error) {
    console.error('Error creating guest review:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
