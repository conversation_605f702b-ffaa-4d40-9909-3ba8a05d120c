import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db';
import { guests } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { getOrCreateUser } from '@/lib/auth-utils';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    try {
      await getOrCreateUser(userId);
    } catch (error) {
      console.error('Error getting/creating user:', error);
      return NextResponse.json({ error: 'Failed to authenticate user' }, { status: 500 });
    }

    // Get guest by ID
    const guest = await db.query.guests.findFirst({
      where: eq(guests.id, params.id),
    });

    if (!guest) {
      return NextResponse.json({ error: 'Guest not found' }, { status: 404 });
    }

    return NextResponse.json(guest);
  } catch (error) {
    console.error('Error fetching guest:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    try {
      await getOrCreateUser(userId);
    } catch (error) {
      console.error('Error getting/creating user:', error);
      return NextResponse.json({ error: 'Failed to authenticate user' }, { status: 500 });
    }

    const body = await request.json();
    const {
      firstName,
      lastName,
      email,
      phone,
      dateOfBirth,
      nationality,
      documentType,
      documentNumber,
      emergencyContact,
      emergencyPhone,
      notes
    } = body;

    if (!firstName || !lastName) {
      return NextResponse.json({ error: 'First name and last name are required' }, { status: 400 });
    }

    // Update guest
    const [updatedGuest] = await db
      .update(guests)
      .set({
        firstName,
        lastName,
        email,
        phone,
        dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
        nationality,
        documentType,
        documentNumber,
        emergencyContact,
        emergencyPhone,
        notes,
        updatedAt: new Date(),
      })
      .where(eq(guests.id, params.id))
      .returning();

    if (!updatedGuest) {
      return NextResponse.json({ error: 'Guest not found' }, { status: 404 });
    }

    return NextResponse.json(updatedGuest);
  } catch (error) {
    console.error('Error updating guest:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    try {
      await getOrCreateUser(userId);
    } catch (error) {
      console.error('Error getting/creating user:', error);
      return NextResponse.json({ error: 'Failed to authenticate user' }, { status: 500 });
    }

    // Delete guest
    const [deletedGuest] = await db
      .delete(guests)
      .where(eq(guests.id, params.id))
      .returning();

    if (!deletedGuest) {
      return NextResponse.json({ error: 'Guest not found' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Guest deleted successfully' });
  } catch (error) {
    console.error('Error deleting guest:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
