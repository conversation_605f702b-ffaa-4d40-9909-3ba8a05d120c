import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db';
import { guests } from '@/db/schema';
import { or, ilike, desc, count } from 'drizzle-orm';
import { getOrCreateUser } from '@/lib/auth-utils';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // Get user from database, create if doesn't exist
    try {
      await getOrCreateUser(userId);
    } catch (error) {
      console.error('Error getting/creating user:', error);
      return NextResponse.json({ error: 'Failed to authenticate user' }, { status: 500 });
    }

    // Build where condition
    const whereCondition = search ? or(
      ilike(guests.firstName, `%${search}%`),
      ilike(guests.lastName, `%${search}%`),
      ilike(guests.email, `%${search}%`)
    ) : undefined;

    // Get total count
    const totalResult = await db
      .select({ count: count() })
      .from(guests)
      .where(whereCondition);

    const total = totalResult[0]?.count || 0;

    // Get paginated data
    const guestsList = await db
      .select()
      .from(guests)
      .where(whereCondition)
      .orderBy(desc(guests.createdAt))
      .limit(limit)
      .offset(offset);

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return NextResponse.json({
      data: guestsList,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    });
  } catch (error) {
    console.error('Error fetching guests:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    try {
      await getOrCreateUser(userId);
    } catch (error) {
      console.error('Error getting/creating user:', error);
      return NextResponse.json({ error: 'Failed to authenticate user' }, { status: 500 });
    }

    const body = await request.json();
    const {
      firstName,
      lastName,
      email,
      phone,
      dateOfBirth,
      nationality,
      documentType,
      documentNumber,
      emergencyContact,
      emergencyPhone,
      notes
    } = body;

    if (!firstName || !lastName) {
      return NextResponse.json({ error: 'First name and last name are required' }, { status: 400 });
    }

    // Create guest
    const [guest] = await db.insert(guests).values({
      firstName,
      lastName,
      email,
      phone,
      dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
      nationality,
      documentType,
      documentNumber,
      emergencyContact,
      emergencyPhone,
      notes,
    }).returning();

    return NextResponse.json(guest, { status: 201 });
  } catch (error) {
    console.error('Error creating guest:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
