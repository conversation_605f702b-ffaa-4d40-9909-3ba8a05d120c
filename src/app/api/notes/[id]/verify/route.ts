import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db';
import { noteVerifications, guestNotes, users } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { getOrCreateUser } from '@/lib/auth-utils';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    const user = await getOrCreateUser(userId);

    const body = await request.json();
    const { comment, isConfirming } = body;

    if (!comment) {
      return NextResponse.json({ error: 'Comment is required' }, { status: 400 });
    }

    // Check if the note exists
    const note = await db.query.guestNotes.findFirst({
      where: eq(guestNotes.id, params.id),
    });

    if (!note) {
      return NextResponse.json({ error: 'Note not found' }, { status: 404 });
    }

    // Check if user has already verified this note
    const existingVerification = await db.query.noteVerifications.findFirst({
      where: and(
        eq(noteVerifications.noteId, params.id),
        eq(noteVerifications.verifierId, user.id)
      ),
    });

    if (existingVerification) {
      return NextResponse.json({ error: 'You have already verified this note' }, { status: 400 });
    }

    // Create the verification
    const [newVerification] = await db
      .insert(noteVerifications)
      .values({
        noteId: params.id,
        verifierId: user.id,
        organizationId: note.organizationId,
        comment,
        isConfirming: isConfirming ?? true,
      })
      .returning();

    // Update the note's verification count
    const verificationCount = isConfirming 
      ? note.verificationCount + 1 
      : note.verificationCount;

    await db
      .update(guestNotes)
      .set({
        verificationCount,
        isVerified: verificationCount >= 2, // Mark as verified if 2+ confirmations
        updatedAt: new Date(),
      })
      .where(eq(guestNotes.id, params.id));

    // Return the verification with verifier information
    const verificationWithVerifier = await db
      .select({
        id: noteVerifications.id,
        noteId: noteVerifications.noteId,
        verifierId: noteVerifications.verifierId,
        organizationId: noteVerifications.organizationId,
        comment: noteVerifications.comment,
        isConfirming: noteVerifications.isConfirming,
        createdAt: noteVerifications.createdAt,
        verifier: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
          email: users.email,
          imageUrl: users.imageUrl,
        },
      })
      .from(noteVerifications)
      .leftJoin(users, eq(noteVerifications.verifierId, users.id))
      .where(eq(noteVerifications.id, newVerification.id))
      .limit(1);

    return NextResponse.json(verificationWithVerifier[0]);
  } catch (error) {
    console.error('Error creating note verification:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get verifications for the note with verifier information
    const verifications = await db
      .select({
        id: noteVerifications.id,
        noteId: noteVerifications.noteId,
        verifierId: noteVerifications.verifierId,
        organizationId: noteVerifications.organizationId,
        comment: noteVerifications.comment,
        isConfirming: noteVerifications.isConfirming,
        createdAt: noteVerifications.createdAt,
        verifier: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
          email: users.email,
          imageUrl: users.imageUrl,
        },
      })
      .from(noteVerifications)
      .leftJoin(users, eq(noteVerifications.verifierId, users.id))
      .where(eq(noteVerifications.noteId, params.id))
      .orderBy(noteVerifications.createdAt);

    return NextResponse.json(verifications);
  } catch (error) {
    console.error('Error fetching note verifications:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
