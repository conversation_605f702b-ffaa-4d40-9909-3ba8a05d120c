import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db';
import { properties, organizations, organizationMembers } from '@/db/schema';
import { eq, desc } from 'drizzle-orm';
import { getOrCreateUser } from '@/lib/auth-utils';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    const user = await getOrCreateUser(userId);

    // Check if user is member of the organization
    const membership = await db.query.organizationMembers.findFirst({
      where: eq(organizationMembers.userId, user.id) && eq(organizationMembers.organizationId, params.id),
    });

    if (!membership) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get properties for the organization
    const organizationProperties = await db
      .select({
        id: properties.id,
        organizationId: properties.organizationId,
        name: properties.name,
        type: properties.type,
        category: properties.category,
        description: properties.description,
        address: properties.address,
        city: properties.city,
        state: properties.state,
        country: properties.country,
        postalCode: properties.postalCode,
        latitude: properties.latitude,
        longitude: properties.longitude,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        maxGuests: properties.maxGuests,
        area: properties.area,
        areaUnit: properties.areaUnit,
        amenities: properties.amenities,
        images: properties.images,
        isActive: properties.isActive,
        isListed: properties.isListed,
        createdAt: properties.createdAt,
        updatedAt: properties.updatedAt,
      })
      .from(properties)
      .where(eq(properties.organizationId, params.id))
      .orderBy(desc(properties.createdAt));

    return NextResponse.json(organizationProperties);
  } catch (error) {
    console.error('Error fetching organization properties:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    const user = await getOrCreateUser(userId);

    // Check if user is member of the organization
    const membership = await db.query.organizationMembers.findFirst({
      where: eq(organizationMembers.userId, user.id) && eq(organizationMembers.organizationId, params.id),
    });

    if (!membership) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const {
      name,
      type,
      category,
      description,
      address,
      city,
      state,
      country,
      postalCode,
      bedrooms,
      bathrooms,
      maxGuests,
      area,
      areaUnit,
      amenities
    } = body;

    if (!name || !type || !category || !address || !city || !country || !bedrooms || !bathrooms || !maxGuests) {
      return NextResponse.json({
        error: 'Missing required fields: name, type, category, address, city, country, bedrooms, bathrooms, maxGuests'
      }, { status: 400 });
    }

    // Create the property
    const [newProperty] = await db
      .insert(properties)
      .values({
        organizationId: params.id,
        name,
        type,
        category,
        description,
        address,
        city,
        state,
        country,
        postalCode,
        bedrooms: parseInt(bedrooms),
        bathrooms: parseFloat(bathrooms),
        maxGuests: parseInt(maxGuests),
        area: area ? parseInt(area) : undefined,
        areaUnit: areaUnit || undefined,
        amenities: amenities || [],
        images: [],
      })
      .returning();

    return NextResponse.json(newProperty);
  } catch (error) {
    console.error('Error creating property:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
