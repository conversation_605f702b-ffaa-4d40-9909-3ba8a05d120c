import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db';
import { organizations, organizationMembers, users } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { updateClerkOrganization, deleteClerkOrganization } from '@/lib/clerk-organizations';
import { CreateOrganizationData } from '@/features/organizations/types';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user is a member of the organization
    const membership = await db.query.organizationMembers.findFirst({
      where: and(
        eq(organizationMembers.userId, user.id),
        eq(organizationMembers.organizationId, params.id)
      ),
    });

    if (!membership) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get organization
    const organization = await db.query.organizations.findFirst({
      where: eq(organizations.id, params.id),
    });

    if (!organization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    return NextResponse.json(organization);
  } catch (error) {
    console.error('Error fetching organization:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user is an admin or owner of the organization
    const membership = await db.query.organizationMembers.findFirst({
      where: and(
        eq(organizationMembers.userId, user.id),
        eq(organizationMembers.organizationId, params.id)
      ),
    });

    if (!membership || !['admin', 'owner'].includes(membership.role)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Get current organization
    const currentOrganization = await db.query.organizations.findFirst({
      where: eq(organizations.id, params.id),
    });

    if (!currentOrganization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    const body = await request.json();
    const updateData: Partial<CreateOrganizationData> = {
      name: body.name,
      description: body.description,
      address: body.address,
      city: body.city,
      state: body.state,
      country: body.country,
      zipCode: body.zipCode,
      phone: body.phone,
      email: body.email,
      website: body.website,
    };

    // Remove undefined values
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof CreateOrganizationData] === undefined) {
        delete updateData[key as keyof CreateOrganizationData];
      }
    });

    try {
      // Step 1: Update organization in Clerk (if we have a Clerk organization ID)
      if (currentOrganization.clerkOrganizationId) {
        await updateClerkOrganization(currentOrganization.clerkOrganizationId, updateData);
      }

      // Step 2: Update organization in our database
      const [updatedOrganization] = await db
        .update(organizations)
        .set({
          ...updateData,
          updatedAt: new Date(),
        })
        .where(eq(organizations.id, params.id))
        .returning();

      return NextResponse.json(updatedOrganization);
    } catch (error) {
      console.error('Error updating organization:', error);
      return NextResponse.json({ error: 'Failed to update organization' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error updating organization:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database
    const user = await db.query.users.findFirst({
      where: eq(users.clerkId, userId),
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check if user is the owner of the organization
    const membership = await db.query.organizationMembers.findFirst({
      where: and(
        eq(organizationMembers.userId, user.id),
        eq(organizationMembers.organizationId, params.id)
      ),
    });

    if (!membership || membership.role !== 'owner') {
      return NextResponse.json({ error: 'Only organization owners can delete organizations' }, { status: 403 });
    }

    // Get current organization
    const currentOrganization = await db.query.organizations.findFirst({
      where: eq(organizations.id, params.id),
    });

    if (!currentOrganization) {
      return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
    }

    try {
      // Step 1: Delete organization in Clerk (if we have a Clerk organization ID)
      if (currentOrganization.clerkOrganizationId) {
        await deleteClerkOrganization(currentOrganization.clerkOrganizationId);
      }

      // Step 2: Delete organization in our database (cascade will handle related records)
      await db.delete(organizations).where(eq(organizations.id, params.id));

      return NextResponse.json({ message: 'Organization deleted successfully' });
    } catch (error) {
      console.error('Error deleting organization:', error);
      return NextResponse.json({ error: 'Failed to delete organization' }, { status: 500 });
    }
  } catch (error) {
    console.error('Error deleting organization:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
