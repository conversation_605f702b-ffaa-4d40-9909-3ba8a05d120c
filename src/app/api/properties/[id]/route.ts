import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db';
import { properties, organizationMembers } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { getOrCreateUser } from '@/lib/auth-utils';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    const user = await getOrCreateUser(userId);

    // Get property
    const property = await db.query.properties.findFirst({
      where: eq(properties.id, params.id),
    });

    if (!property) {
      return NextResponse.json({ error: 'Property not found' }, { status: 404 });
    }

    // Check if user is member of the organization that owns this property
    const membership = await db.query.organizationMembers.findFirst({
      where: eq(organizationMembers.userId, user.id) && eq(organizationMembers.organizationId, property.organizationId),
    });

    if (!membership) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    return NextResponse.json(property);
  } catch (error) {
    console.error('Error fetching property:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    const user = await getOrCreateUser(userId);

    // Get property
    const property = await db.query.properties.findFirst({
      where: eq(properties.id, params.id),
    });

    if (!property) {
      return NextResponse.json({ error: 'Property not found' }, { status: 404 });
    }

    // Check if user is member of the organization that owns this property
    const membership = await db.query.organizationMembers.findFirst({
      where: eq(organizationMembers.userId, user.id) && eq(organizationMembers.organizationId, property.organizationId),
    });

    if (!membership) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    const body = await request.json();
    const {
      name,
      type,
      category,
      description,
      address,
      city,
      state,
      country,
      postalCode,
      bedrooms,
      bathrooms,
      maxGuests,
      area,
      areaUnit,
      amenities,
      isActive,
      isListed
    } = body;

    // Update the property
    const [updatedProperty] = await db
      .update(properties)
      .set({
        name: name || property.name,
        type: type || property.type,
        category: category || property.category,
        description: description !== undefined ? description : property.description,
        address: address || property.address,
        city: city || property.city,
        state: state !== undefined ? state : property.state,
        country: country || property.country,
        postalCode: postalCode !== undefined ? postalCode : property.postalCode,
        bedrooms: bedrooms !== undefined ? parseInt(bedrooms) : property.bedrooms,
        bathrooms: bathrooms !== undefined ? parseFloat(bathrooms) : property.bathrooms,
        maxGuests: maxGuests !== undefined ? parseInt(maxGuests) : property.maxGuests,
        area: area !== undefined ? (area ? parseInt(area) : null) : property.area,
        areaUnit: areaUnit !== undefined ? areaUnit : property.areaUnit,
        amenities: amenities !== undefined ? amenities : property.amenities,
        isActive: isActive !== undefined ? isActive : property.isActive,
        isListed: isListed !== undefined ? isListed : property.isListed,
        updatedAt: new Date(),
      })
      .where(eq(properties.id, params.id))
      .returning();

    return NextResponse.json(updatedProperty);
  } catch (error) {
    console.error('Error updating property:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    const user = await getOrCreateUser(userId);

    // Get property
    const property = await db.query.properties.findFirst({
      where: eq(properties.id, params.id),
    });

    if (!property) {
      return NextResponse.json({ error: 'Property not found' }, { status: 404 });
    }

    // Check if user is member of the organization that owns this property
    const membership = await db.query.organizationMembers.findFirst({
      where: eq(organizationMembers.userId, user.id) && eq(organizationMembers.organizationId, property.organizationId),
    });

    if (!membership) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Delete the property
    await db.delete(properties).where(eq(properties.id, params.id));

    return NextResponse.json({ message: 'Property deleted successfully' });
  } catch (error) {
    console.error('Error deleting property:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
