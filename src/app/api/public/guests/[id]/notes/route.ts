import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { guestNotes, users, noteVerifications } from '@/db/schema';
import { eq, desc } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get notes for the guest with author information (public view)
    const notes = await db
      .select({
        id: guestNotes.id,
        guestId: guestNotes.guestId,
        title: guestNotes.title,
        content: guestNotes.content,
        category: guestNotes.category,
        severity: guestNotes.severity,
        isVerified: guestNotes.isVerified,
        verificationCount: guestNotes.verificationCount,
        createdAt: guestNotes.createdAt,
        author: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
        },
      })
      .from(guestNotes)
      .leftJoin(users, eq(guestNotes.authorId, users.id))
      .where(eq(guestNotes.guestId, params.id))
      .orderBy(desc(guestNotes.createdAt));

    // Get verifications for each note
    const notesWithVerifications = await Promise.all(
      notes.map(async (note) => {
        const verifications = await db
          .select({
            id: noteVerifications.id,
            comment: noteVerifications.comment,
            isConfirming: noteVerifications.isConfirming,
            createdAt: noteVerifications.createdAt,
            verifier: {
              id: users.id,
              firstName: users.firstName,
              lastName: users.lastName,
            },
          })
          .from(noteVerifications)
          .leftJoin(users, eq(noteVerifications.verifierId, users.id))
          .where(eq(noteVerifications.noteId, note.id))
          .orderBy(noteVerifications.createdAt);

        return {
          ...note,
          verifications,
        };
      })
    );

    return NextResponse.json(notesWithVerifications);
  } catch (error) {
    console.error('Error fetching public guest notes:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
