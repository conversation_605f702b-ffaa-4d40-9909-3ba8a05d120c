import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { reviews, users } from '@/db/schema';
import { eq, desc } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get reviews for the guest with reviewer information (public view)
    const guestReviews = await db
      .select({
        id: reviews.id,
        guestId: reviews.guestId,
        rating: reviews.rating,
        title: reviews.title,
        comment: reviews.comment,
        stayDates: reviews.stayDates,
        propertyType: reviews.propertyType,
        wouldRecommend: reviews.wouldRecommend,
        tags: reviews.tags,
        createdAt: reviews.createdAt,
        reviewer: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
        },
      })
      .from(reviews)
      .leftJoin(users, eq(reviews.reviewerId, users.id))
      .where(eq(reviews.guestId, params.id))
      .orderBy(desc(reviews.createdAt));

    return NextResponse.json(guestReviews);
  } catch (error) {
    console.error('Error fetching public guest reviews:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
