import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { guests } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get guest by ID (public information only)
    const guest = await db
      .select({
        id: guests.id,
        firstName: guests.firstName,
        lastName: guests.lastName,
        nationality: guests.nationality,
        averageRating: guests.averageRating,
        totalReviews: guests.totalReviews,
        createdAt: guests.createdAt,
      })
      .from(guests)
      .where(eq(guests.id, params.id))
      .limit(1);

    if (!guest[0]) {
      return NextResponse.json({ error: 'Guest not found' }, { status: 404 });
    }

    return NextResponse.json(guest[0]);
  } catch (error) {
    console.error('Error fetching public guest:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
