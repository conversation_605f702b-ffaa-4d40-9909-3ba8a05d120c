import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db';
import { reviews, users, guests, organizationMembers } from '@/db/schema';
import { eq, desc, and } from 'drizzle-orm';
import { getOrCreateUser } from '@/lib/auth-utils';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user from database, create if doesn't exist
    const user = await getOrCreateUser(userId);

    // Get user's organizations
    const userOrganizations = await db.query.organizationMembers.findMany({
      where: eq(organizationMembers.userId, user.id),
    });

    const organizationIds = userOrganizations.map(om => om.organizationId);

    if (organizationIds.length === 0) {
      return NextResponse.json([]);
    }

    // Get all reviews for the user's organizations
    const allReviews = await db
      .select({
        id: reviews.id,
        guestId: reviews.guestId,
        organizationId: reviews.organizationId,
        reviewerId: reviews.reviewerId,
        rating: reviews.rating,
        title: reviews.title,
        comment: reviews.comment,
        stayDates: reviews.stayDates,
        propertyType: reviews.propertyType,
        wouldRecommend: reviews.wouldRecommend,
        tags: reviews.tags,
        images: reviews.images,
        createdAt: reviews.createdAt,
        updatedAt: reviews.updatedAt,
        // Reviewer info
        reviewerFirstName: users.firstName,
        reviewerLastName: users.lastName,
        reviewerEmail: users.email,
        reviewerImageUrl: users.imageUrl,
        // Guest info
        guestFirstName: guests.firstName,
        guestLastName: guests.lastName,
      })
      .from(reviews)
      .leftJoin(users, eq(reviews.reviewerId, users.id))
      .leftJoin(guests, eq(reviews.guestId, guests.id))
      .where(
        and(
          // Only reviews from user's organizations
          organizationIds.length === 1 
            ? eq(reviews.organizationId, organizationIds[0])
            : undefined // TODO: Handle multiple organizations with IN clause
        )
      )
      .orderBy(desc(reviews.createdAt));

    // Transform the data to match the Review interface
    const transformedReviews = allReviews.map(review => ({
      id: review.id,
      guestId: review.guestId,
      organizationId: review.organizationId,
      reviewerId: review.reviewerId,
      rating: review.rating,
      title: review.title,
      comment: review.comment,
      stayDates: review.stayDates,
      propertyType: review.propertyType,
      wouldRecommend: review.wouldRecommend,
      tags: review.tags,
      images: review.images,
      createdAt: review.createdAt,
      updatedAt: review.updatedAt,
      reviewer: review.reviewerFirstName ? {
        id: review.reviewerId,
        firstName: review.reviewerFirstName,
        lastName: review.reviewerLastName,
        email: review.reviewerEmail,
        imageUrl: review.reviewerImageUrl,
      } : undefined,
      guest: review.guestFirstName ? {
        id: review.guestId,
        firstName: review.guestFirstName,
        lastName: review.guestLastName,
      } : undefined,
    }));

    return NextResponse.json(transformedReviews);
  } catch (error) {
    console.error('Error fetching reviews:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
