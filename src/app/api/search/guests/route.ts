import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { guests, guestNotes } from '@/db/schema';
import { or, ilike, desc, count, eq } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 50);
    const offset = parseInt(searchParams.get('offset') || '0');

    if (!query || query.trim().length < 2) {
      return NextResponse.json({ error: 'Search query must be at least 2 characters' }, { status: 400 });
    }

    // Search guests by name, email, or nationality
    const searchResults = await db
      .select({
        id: guests.id,
        firstName: guests.firstName,
        lastName: guests.lastName,
        email: guests.email,
        nationality: guests.nationality,
        averageRating: guests.averageRating,
        totalReviews: guests.totalReviews,
        createdAt: guests.createdAt,
      })
      .from(guests)
      .where(
        or(
          ilike(guests.firstName, `%${query}%`),
          ilike(guests.lastName, `%${query}%`),
          ilike(guests.email, `%${query}%`),
          ilike(guests.nationality, `%${query}%`)
        )
      )
      .orderBy(desc(guests.createdAt))
      .limit(limit)
      .offset(offset);

    // Get notes count for each guest
    const guestsWithNotesCount = await Promise.all(
      searchResults.map(async (guest) => {
        const notesCountResult = await db
          .select({ count: count() })
          .from(guestNotes)
          .where(eq(guestNotes.guestId, guest.id));
        
        return {
          ...guest,
          notesCount: notesCountResult[0]?.count || 0,
        };
      })
    );

    return NextResponse.json(guestsWithNotesCount);
  } catch (error) {
    console.error('Error searching guests:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
