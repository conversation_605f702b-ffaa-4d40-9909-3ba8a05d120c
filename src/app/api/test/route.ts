import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function GET() {
  try {
    const { userId } = await auth();
    
    return NextResponse.json({
      success: true,
      authenticated: !!userId,
      userId: userId || null,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Test API error:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error',
      authenticated: false 
    }, { status: 500 });
  }
}
