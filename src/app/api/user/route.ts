import { NextRequest, NextResponse } from "next/server";
import { db } from "@/db";
import { users } from "@/db/schema";
import { eq } from "drizzle-orm";
import { auth, clerkClient } from "@clerk/nextjs/server";
import { getOrCreateUser } from "@/lib/auth-utils";

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await getOrCreateUser(userId);
    return NextResponse.json(user);
  } catch (error: any) {
    console.error("Error fetching user:", error);
    // Detect Neon rate limit error
    if (
      error.message &&
      error.message.includes("exceeded the rate limit")
    ) {
      return NextResponse.json(
        { error: "Rate limit exceeded. Please wait a moment and try again.", code: "RATE_LIMIT_EXCEEDED" },
        { status: 429 }
      );
    }
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const updateData = await request.json();
    const { firstName, lastName, email, password, ...otherUpdates } = updateData;
    
    console.log("PUT /api/user request body:", JSON.stringify(updateData, null, 2));
    
    // Get current user from database
    const user = await getOrCreateUser(userId);
    
    // First update our database
    const dbUpdateData = {
      ...otherUpdates,
      updatedAt: new Date(),
    };
    
    // Add firstName and lastName if provided, ensuring we don't set them to undefined
    if (firstName !== undefined) {
      dbUpdateData.firstName = firstName;
      console.log(`Updating firstName to: "${firstName}"`);
    }
    
    if (lastName !== undefined) {
      dbUpdateData.lastName = lastName;
      console.log(`Updating lastName to: "${lastName}"`);
    }

    if (email !== undefined) {
      dbUpdateData.email = email;
      console.log(`Updating email to: "${email}"`);
    }
    
    console.log("Database update data:", JSON.stringify(dbUpdateData, null, 2));
    
    const updatedUser = await db
      .update(users)
      .set(dbUpdateData)
      .where(eq(users.id, user.id))
      .returning();

    if (updatedUser.length === 0) {
      console.error("User not found in database:", userId);
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    console.log("Updated user in database:", JSON.stringify(updatedUser[0], null, 2));
    
    // Update Clerk user with any provided data
    const clerkUpdateData: any = {};
    
    if (firstName !== undefined) {
      clerkUpdateData.firstName = firstName;
    }
    
    if (lastName !== undefined) {
      clerkUpdateData.lastName = lastName;
    }

    if (email !== undefined) {
      clerkUpdateData.primaryEmailAddressID = email;
    }

    if (password !== undefined) {
      clerkUpdateData.password = password;
    }
    
    // Update Clerk if there's data to update
    if (Object.keys(clerkUpdateData).length > 0) {
      try {
        await clerkClient.users.updateUser(userId, clerkUpdateData);
        console.log("Updated user in Clerk:", JSON.stringify(clerkUpdateData, null, 2));
      } catch (clerkError) {
        console.error("Error updating Clerk user:", clerkError);
        return NextResponse.json(
          { error: "Failed to update user profile in authentication system" },
          { status: 500 }
        );
      }
    }

    return NextResponse.json(updatedUser[0]);
  } catch (error) {
    console.error("Error updating user:", error);
    return NextResponse.json(
      { error: "Internal Server Error" },
      { status: 500 }
    );
  }
}
