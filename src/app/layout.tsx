import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs";
import { ptBR, enUS, esES } from "@clerk/localizations";
import { NextIntlClientProvider } from "next-intl";
import { headers } from "next/headers";
import { cn } from "@/lib/utils";
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from "next/font/google";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as SonnerToaster } from "sonner";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// Import all language files
import enMessages from "@/languages/en.json";
import brMessages from "@/languages/br.json";
import esMessages from "@/languages/es.json";

type Locale = "en" | "br" | "es";

// Create a more flexible type for messages
type DeepPartial<T> = T extends object
  ? {
      [P in keyof T]?: DeepPartial<T[P]>;
    }
  : T;

type MessageType = typeof enMessages;
type FlexibleMessageType = DeepPartial<MessageType>;

const messages: Record<Locale, FlexibleMessageType> = {
  en: enMessages,
  br: brMessages,
  es: esMessages,
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Determine the locale from the headers
  const headersList = await headers();
  const pathname = headersList.get("x-invoke-path") || "";
  let locale = (pathname.split("/")[1] || "en") as Locale;

  // Validate the locale and fallback to 'en' if invalid
  if (!["en", "br", "es"].includes(locale)) {
    console.error(`Unsupported locale: ${locale}, falling back to 'en'`);
    locale = "en";
  }

  const clerkLocale = locale === "br" ? ptBR : locale === "es" ? esES : enUS;

  return (
    <html lang={locale} className="scroll-smooth">
      <ClerkProvider
        localization={clerkLocale}
        afterSignOutUrl={`/${locale}`}
        signInUrl={`/${locale}/sign-in`}
        signUpUrl={`/${locale}/sign-up`}
        afterSignInUrl={`/${locale}/dashboard`}
        afterSignUpUrl={`/${locale}/dashboard`}
        appearance={{
          elements: {
            // Disable CAPTCHA in development
            captcha: { display: 'none' }
          }
        }}
      >
        <NextIntlClientProvider messages={messages[locale]} locale={locale}>
          <body
            className={cn(
              "min-h-screen bg-[#f5f9fa] font-sans antialiased",
              geistSans.variable,
              geistMono.variable
            )}
          >
            {children}
            <Toaster />
            <SonnerToaster />
          </body>
        </NextIntlClientProvider>
      </ClerkProvider>
    </html>
  );
}

// Define the locales you want to support
export function generateStaticParams() {
  return [{ locale: "en" }, { locale: "br" }, { locale: "es" }];
}
