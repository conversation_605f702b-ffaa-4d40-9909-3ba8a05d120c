"use client";

import Link from "next/link";
import { usePathname, useParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { UserButton } from "@clerk/nextjs";
import { Building2, Users, Star, User, Home, MapPin } from "lucide-react";

export function MainNav() {
  const pathname = usePathname();
  const params = useParams();
  const locale = params.locale as string;
  const t = useTranslations("navigation");

  const navigation = [
    {
      name: t("dashboard"),
      href: `/${locale}/dashboard`,
      icon: Home,
    },
    {
      name: t("properties"),
      href: `/${locale}/dashboard/properties`,
      icon: MapPin,
    },
    {
      name: t("guests"),
      href: `/${locale}/dashboard/guests`,
      icon: Users,
    },
    {
      name: t("reviews"),
      href: `/${locale}/dashboard/reviews`,
      icon: Star,
    },
    {
      name: t("organizations"),
      href: `/${locale}/dashboard/organizations`,
      icon: Building2,
    },
    {
      name: t("account"),
      href: `/${locale}/dashboard/account`,
      icon: User,
    },
  ];

  return (
    <header className="border-b border-gray-200 bg-white">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 justify-between items-center">
          <div className="flex items-center">
            <Link href={`/${locale}/dashboard`} className="flex items-center space-x-2">
              <div className="h-8 w-8 bg-airbnb-rausch rounded-lg flex items-center justify-center">
                <Building2 className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-airbnb-hof">GuestFile</span>
            </Link>
          </div>

          <nav className="hidden md:flex space-x-8">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors",
                    pathname === item.href
                      ? "bg-airbnb-rausch text-white"
                      : "text-airbnb-hof hover:text-airbnb-rausch hover:bg-airbnb-light"
                  )}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </nav>

          <div className="flex items-center space-x-4">
            <UserButton
              appearance={{
                elements: {
                  avatarBox: "h-8 w-8",
                }
              }}
            />
          </div>
        </div>
      </div>
    </header>
  );
}
