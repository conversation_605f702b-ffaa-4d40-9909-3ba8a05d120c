{"id": "c4325eb8-6a89-4736-8b72-4ca60d2d0bea", "prevId": "b3a783e8-e89b-4e61-8dfe-8d2e68425311", "version": "7", "dialect": "postgresql", "tables": {"public.guest_notes": {"name": "guest_notes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "guest_id": {"name": "guest_id", "type": "uuid", "primaryKey": false, "notNull": true}, "author_id": {"name": "author_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false, "default": "'general'"}, "severity": {"name": "severity", "type": "text", "primaryKey": false, "notNull": false, "default": "'low'"}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "verification_count": {"name": "verification_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"guest_notes_guest_id_guests_id_fk": {"name": "guest_notes_guest_id_guests_id_fk", "tableFrom": "guest_notes", "tableTo": "guests", "columnsFrom": ["guest_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "guest_notes_author_id_users_id_fk": {"name": "guest_notes_author_id_users_id_fk", "tableFrom": "guest_notes", "tableTo": "users", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "guest_notes_organization_id_organizations_id_fk": {"name": "guest_notes_organization_id_organizations_id_fk", "tableFrom": "guest_notes", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.guests": {"name": "guests", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "timestamp", "primaryKey": false, "notNull": false}, "nationality": {"name": "nationality", "type": "text", "primaryKey": false, "notNull": false}, "document_type": {"name": "document_type", "type": "text", "primaryKey": false, "notNull": false}, "document_number": {"name": "document_number", "type": "text", "primaryKey": false, "notNull": false}, "emergency_contact": {"name": "emergency_contact", "type": "text", "primaryKey": false, "notNull": false}, "emergency_phone": {"name": "emergency_phone", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "average_rating": {"name": "average_rating", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_reviews": {"name": "total_reviews", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.note_verifications": {"name": "note_verifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "note_id": {"name": "note_id", "type": "uuid", "primaryKey": false, "notNull": true}, "verifier_id": {"name": "verifier_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": true}, "is_confirming": {"name": "is_confirming", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"note_verifications_note_id_guest_notes_id_fk": {"name": "note_verifications_note_id_guest_notes_id_fk", "tableFrom": "note_verifications", "tableTo": "guest_notes", "columnsFrom": ["note_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "note_verifications_verifier_id_users_id_fk": {"name": "note_verifications_verifier_id_users_id_fk", "tableFrom": "note_verifications", "tableTo": "users", "columnsFrom": ["verifier_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "note_verifications_organization_id_organizations_id_fk": {"name": "note_verifications_organization_id_organizations_id_fk", "tableFrom": "note_verifications", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization_members": {"name": "organization_members", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'member'"}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"organization_members_user_id_users_id_fk": {"name": "organization_members_user_id_users_id_fk", "tableFrom": "organization_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "organization_members_organization_id_organizations_id_fk": {"name": "organization_members_organization_id_organizations_id_fk", "tableFrom": "organization_members", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organizations": {"name": "organizations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "clerk_organization_id": {"name": "clerk_organization_id", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false}, "zip_code": {"name": "zip_code", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "text", "primaryKey": false, "notNull": false}, "subscription_status": {"name": "subscription_status", "type": "text", "primaryKey": false, "notNull": false, "default": "'inactive'"}, "subscription_id": {"name": "subscription_id", "type": "text", "primaryKey": false, "notNull": false}, "max_users": {"name": "max_users", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organizations_clerk_organization_id_unique": {"name": "organizations_clerk_organization_id_unique", "nullsNotDistinct": false, "columns": ["clerk_organization_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.reviews": {"name": "reviews", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "guest_id": {"name": "guest_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "reviewer_id": {"name": "reviewer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}, "stay_dates": {"name": "stay_dates", "type": "jsonb", "primaryKey": false, "notNull": false}, "property_type": {"name": "property_type", "type": "text", "primaryKey": false, "notNull": false}, "would_recommend": {"name": "would_recommend", "type": "boolean", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}, "images": {"name": "images", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"reviews_guest_id_guests_id_fk": {"name": "reviews_guest_id_guests_id_fk", "tableFrom": "reviews", "tableTo": "guests", "columnsFrom": ["guest_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "reviews_organization_id_organizations_id_fk": {"name": "reviews_organization_id_organizations_id_fk", "tableFrom": "reviews", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "reviews_reviewer_id_users_id_fk": {"name": "reviews_reviewer_id_users_id_fk", "tableFrom": "reviews", "tableTo": "users", "columnsFrom": ["reviewer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.stays": {"name": "stays", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "guest_id": {"name": "guest_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": true}, "property_name": {"name": "property_name", "type": "text", "primaryKey": false, "notNull": false}, "check_in_date": {"name": "check_in_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "check_out_date": {"name": "check_out_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "number_of_guests": {"name": "number_of_guests", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "total_amount": {"name": "total_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'USD'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'confirmed'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"stays_guest_id_guests_id_fk": {"name": "stays_guest_id_guests_id_fk", "tableFrom": "stays", "tableTo": "guests", "columnsFrom": ["guest_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "stays_organization_id_organizations_id_fk": {"name": "stays_organization_id_organizations_id_fk", "tableFrom": "stays", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "clerk_id": {"name": "clerk_id", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_clerk_id_unique": {"name": "users_clerk_id_unique", "nullsNotDistinct": false, "columns": ["clerk_id"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}