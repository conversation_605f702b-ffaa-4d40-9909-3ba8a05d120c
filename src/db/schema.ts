import { pgTable, text, timestamp, uuid, integer, decimal, boolean, jsonb, varchar, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Users table
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  clerkId: text('clerk_id').unique().notNull(),
  email: text('email').unique().notNull(),
  firstName: text('first_name'),
  lastName: text('last_name'),
  imageUrl: text('image_url'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Organizations table
export const organizations = pgTable('organizations', {
  id: uuid('id').primaryKey().defaultRandom(),
  clerkOrganizationId: text('clerk_organization_id').unique(),
  name: text('name').notNull(),
  description: text('description'),
  address: text('address'),
  city: text('city'),
  state: text('state'),
  country: text('country'),
  zipCode: text('zip_code'),
  phone: text('phone'),
  email: text('email'),
  website: text('website'),
  imageUrl: text('image_url'),
  stripeCustomerId: text('stripe_customer_id'),
  subscriptionStatus: text('subscription_status').default('inactive'),
  subscriptionId: text('subscription_id'),
  maxUsers: integer('max_users').default(1),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Organization members junction table
export const organizationMembers = pgTable('organization_members', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  organizationId: uuid('organization_id').references(() => organizations.id, { onDelete: 'cascade' }).notNull(),
  role: text('role').default('member').notNull(), // 'owner', 'admin', 'member'
  joinedAt: timestamp('joined_at').defaultNow().notNull(),
});

// Guests table
export const guests = pgTable('guests', {
  id: uuid('id').primaryKey().defaultRandom(),
  firstName: text('first_name').notNull(),
  lastName: text('last_name').notNull(),
  email: text('email'),
  phone: text('phone'),
  dateOfBirth: timestamp('date_of_birth'),
  nationality: text('nationality'),
  documentType: text('document_type'), // 'passport', 'id', 'driver_license'
  documentNumber: text('document_number'),
  emergencyContact: text('emergency_contact'),
  emergencyPhone: text('emergency_phone'),
  notes: text('notes'),
  imageUrl: text('image_url'),
  averageRating: decimal('average_rating', { precision: 3, scale: 2 }).default('0'),
  totalReviews: integer('total_reviews').default(0),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Reviews table
export const reviews = pgTable('reviews', {
  id: uuid('id').primaryKey().defaultRandom(),
  guestId: uuid('guest_id').references(() => guests.id, { onDelete: 'cascade' }).notNull(),
  organizationId: uuid('organization_id').references(() => organizations.id, { onDelete: 'cascade' }).notNull(),
  reviewerId: uuid('reviewer_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  rating: integer('rating').notNull(), // 1-5
  title: text('title'),
  comment: text('comment'),
  stayDates: jsonb('stay_dates'), // { checkIn: date, checkOut: date }
  propertyType: text('property_type'), // 'apartment', 'house', 'room'
  wouldRecommend: boolean('would_recommend'),
  tags: text('tags').array(), // ['clean', 'quiet', 'respectful', 'problematic']
  images: jsonb('images'), // Array of image objects: [{ url: string, key: string, caption?: string }]
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Guest Notes table (for fact verification system)
export const guestNotes = pgTable('guest_notes', {
  id: uuid('id').primaryKey().defaultRandom(),
  guestId: uuid('guest_id').references(() => guests.id, { onDelete: 'cascade' }).notNull(),
  authorId: uuid('author_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  organizationId: uuid('organization_id').references(() => organizations.id, { onDelete: 'cascade' }).notNull(),
  title: text('title').notNull(),
  content: text('content').notNull(),
  category: text('category').default('general'), // 'cleanliness', 'behavior', 'damage', 'general'
  severity: text('severity').default('low'), // 'low', 'medium', 'high'
  isVerified: boolean('is_verified').default(false),
  verificationCount: integer('verification_count').default(0),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Note Verifications table (users confirming notes)
export const noteVerifications = pgTable('note_verifications', {
  id: uuid('id').primaryKey().defaultRandom(),
  noteId: uuid('note_id').references(() => guestNotes.id, { onDelete: 'cascade' }).notNull(),
  verifierId: uuid('verifier_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  organizationId: uuid('organization_id').references(() => organizations.id, { onDelete: 'cascade' }).notNull(),
  comment: text('comment').notNull(),
  isConfirming: boolean('is_confirming').default(true), // true = confirming, false = disputing
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Properties table
export const properties = pgTable('properties', {
  id: uuid('id').primaryKey().defaultRandom(),
  organizationId: uuid('organization_id').references(() => organizations.id, { onDelete: 'cascade' }).notNull(),
  name: text('name').notNull(),
  type: text('type').notNull(), // PropertyType enum
  category: text('category').notNull(), // PropertyCategory enum
  description: text('description'),

  // Location
  address: text('address').notNull(),
  city: text('city').notNull(),
  state: text('state'),
  country: text('country').notNull(),
  postalCode: text('postal_code'),
  latitude: decimal('latitude', { precision: 10, scale: 8 }),
  longitude: decimal('longitude', { precision: 11, scale: 8 }),

  // Property Details
  bedrooms: integer('bedrooms').notNull(),
  bathrooms: decimal('bathrooms', { precision: 3, scale: 1 }).notNull(),
  maxGuests: integer('max_guests').notNull(),
  area: integer('area'),
  areaUnit: text('area_unit'), // 'sqft' or 'sqm'

  // Amenities
  amenities: text('amenities').array().default([]),

  // Images
  images: jsonb('images').default([]), // Array of PropertyImage objects

  // Status
  isActive: boolean('is_active').default(true).notNull(),
  isListed: boolean('is_listed').default(false).notNull(),

  // Metadata
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Bookings/Stays table (optional for tracking)
export const stays = pgTable('stays', {
  id: uuid('id').primaryKey().defaultRandom(),
  guestId: uuid('guest_id').references(() => guests.id, { onDelete: 'cascade' }).notNull(),
  propertyId: uuid('property_id').references(() => properties.id, { onDelete: 'cascade' }).notNull(),
  organizationId: uuid('organization_id').references(() => organizations.id, { onDelete: 'cascade' }).notNull(),
  checkInDate: timestamp('check_in_date').notNull(),
  checkOutDate: timestamp('check_out_date').notNull(),
  numberOfGuests: integer('number_of_guests').default(1),
  totalAmount: decimal('total_amount', { precision: 10, scale: 2 }),
  currency: text('currency').default('USD'),
  status: text('status').default('confirmed'), // 'confirmed', 'checked_in', 'checked_out', 'cancelled'
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  organizationMembers: many(organizationMembers),
  reviews: many(reviews),
}));

export const organizationsRelations = relations(organizations, ({ many }) => ({
  members: many(organizationMembers),
  properties: many(properties),
  reviews: many(reviews),
  stays: many(stays),
}));

export const organizationMembersRelations = relations(organizationMembers, ({ one }) => ({
  user: one(users, {
    fields: [organizationMembers.userId],
    references: [users.id],
  }),
  organization: one(organizations, {
    fields: [organizationMembers.organizationId],
    references: [organizations.id],
  }),
}));

export const guestsRelations = relations(guests, ({ many }) => ({
  reviews: many(reviews),
  stays: many(stays),
  notes: many(guestNotes),
}));

export const reviewsRelations = relations(reviews, ({ one }) => ({
  guest: one(guests, {
    fields: [reviews.guestId],
    references: [guests.id],
  }),
  organization: one(organizations, {
    fields: [reviews.organizationId],
    references: [organizations.id],
  }),
  reviewer: one(users, {
    fields: [reviews.reviewerId],
    references: [users.id],
  }),
}));

export const propertiesRelations = relations(properties, ({ one, many }) => ({
  organization: one(organizations, {
    fields: [properties.organizationId],
    references: [organizations.id],
  }),
  stays: many(stays),
}));

export const staysRelations = relations(stays, ({ one }) => ({
  guest: one(guests, {
    fields: [stays.guestId],
    references: [guests.id],
  }),
  property: one(properties, {
    fields: [stays.propertyId],
    references: [properties.id],
  }),
  organization: one(organizations, {
    fields: [stays.organizationId],
    references: [organizations.id],
  }),
}));

export const guestNotesRelations = relations(guestNotes, ({ one, many }) => ({
  guest: one(guests, {
    fields: [guestNotes.guestId],
    references: [guests.id],
  }),
  author: one(users, {
    fields: [guestNotes.authorId],
    references: [users.id],
  }),
  organization: one(organizations, {
    fields: [guestNotes.organizationId],
    references: [organizations.id],
  }),
  verifications: many(noteVerifications),
}));

export const noteVerificationsRelations = relations(noteVerifications, ({ one }) => ({
  note: one(guestNotes, {
    fields: [noteVerifications.noteId],
    references: [guestNotes.id],
  }),
  verifier: one(users, {
    fields: [noteVerifications.verifierId],
    references: [users.id],
  }),
  organization: one(organizations, {
    fields: [noteVerifications.organizationId],
    references: [organizations.id],
  }),
}));
