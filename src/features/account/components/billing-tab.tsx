"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  CreditCard, 
  Calendar, 
  Download, 
  ExternalLink,
  Check,
  X,
  AlertCircle,
  Crown,
  Zap
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface PaymentHistory {
  id: string;
  date: string;
  amount: number;
  currency: string;
  status: 'paid' | 'pending' | 'failed';
  description: string;
  invoiceUrl?: string;
}

interface Subscription {
  id: string;
  plan: 'free' | 'pro' | 'enterprise';
  status: 'active' | 'canceled' | 'past_due';
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
}

export function BillingTab() {
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<PaymentHistory[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchBillingData();
  }, []);

  const fetchBillingData = async () => {
    try {
      // Mock data for now - replace with actual API calls
      setSubscription({
        id: 'sub_1234567890',
        plan: 'pro',
        status: 'active',
        currentPeriodStart: '2024-01-01',
        currentPeriodEnd: '2024-02-01',
        cancelAtPeriodEnd: false,
      });

      setPaymentHistory([
        {
          id: 'inv_001',
          date: '2024-01-01',
          amount: 29.99,
          currency: 'USD',
          status: 'paid',
          description: 'Pro Plan - Monthly',
          invoiceUrl: '#',
        },
        {
          id: 'inv_002',
          date: '2023-12-01',
          amount: 29.99,
          currency: 'USD',
          status: 'paid',
          description: 'Pro Plan - Monthly',
          invoiceUrl: '#',
        },
        {
          id: 'inv_003',
          date: '2023-11-01',
          amount: 29.99,
          currency: 'USD',
          status: 'paid',
          description: 'Pro Plan - Monthly',
          invoiceUrl: '#',
        },
      ]);
    } catch (error) {
      console.error('Error fetching billing data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    // Implement subscription cancellation
    console.log('Cancel subscription');
  };

  const handleUpgrade = (plan: string) => {
    // Implement plan upgrade
    console.log('Upgrade to', plan);
  };

  const getPlanInfo = (plan: string) => {
    switch (plan) {
      case 'free':
        return {
          name: 'Free',
          price: '$0',
          features: ['Up to 10 guests', 'Basic reviews', 'Email support'],
          icon: <Check className="h-4 w-4" />,
          color: 'bg-gray-100 text-gray-800',
        };
      case 'pro':
        return {
          name: 'Pro',
          price: '$29.99',
          features: ['Unlimited guests', 'Advanced analytics', 'Priority support', 'Custom branding'],
          icon: <Crown className="h-4 w-4" />,
          color: 'bg-blue-100 text-blue-800',
        };
      case 'enterprise':
        return {
          name: 'Enterprise',
          price: '$99.99',
          features: ['Everything in Pro', 'API access', 'Dedicated support', 'Custom integrations'],
          icon: <Zap className="h-4 w-4" />,
          color: 'bg-purple-100 text-purple-800',
        };
      default:
        return {
          name: 'Unknown',
          price: '$0',
          features: [],
          icon: <X className="h-4 w-4" />,
          color: 'bg-gray-100 text-gray-800',
        };
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'canceled':
        return <Badge variant="destructive">Canceled</Badge>;
      case 'past_due':
        return <Badge className="bg-yellow-100 text-yellow-800">Past Due</Badge>;
      case 'paid':
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-20 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const currentPlan = subscription ? getPlanInfo(subscription.plan) : null;

  return (
    <div className="space-y-6">
      {/* Current Plan */}
      <Card>
        <CardHeader>
          <CardTitle className="text-airbnb-hof">Current Plan</CardTitle>
        </CardHeader>
        <CardContent>
          {subscription ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${currentPlan?.color}`}>
                    {currentPlan?.icon}
                  </div>
                  <div>
                    <h3 className="font-semibold text-airbnb-hof">{currentPlan?.name} Plan</h3>
                    <p className="text-sm text-airbnb-foggy">
                      {currentPlan?.price}/month
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  {getStatusBadge(subscription.status)}
                  <p className="text-sm text-airbnb-foggy mt-1">
                    Renews on {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-airbnb-hof mb-2">Features</h4>
                  <ul className="space-y-1">
                    {currentPlan?.features.map((feature, index) => (
                      <li key={index} className="flex items-center space-x-2 text-sm">
                        <Check className="h-4 w-4 text-green-600" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="space-y-2">
                  {subscription.plan !== 'enterprise' && (
                    <Button
                      className="w-full bg-airbnb-rausch hover:bg-airbnb-rausch/90"
                      onClick={() => handleUpgrade('enterprise')}
                    >
                      <Crown className="h-4 w-4 mr-2" />
                      Upgrade Plan
                    </Button>
                  )}
                  
                  {subscription.status === 'active' && !subscription.cancelAtPeriodEnd && (
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={handleCancelSubscription}
                    >
                      Cancel Subscription
                    </Button>
                  )}

                  {subscription.cancelAtPeriodEnd && (
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <AlertCircle className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm text-yellow-800">
                          Your subscription will end on {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <CreditCard className="h-12 w-12 text-airbnb-foggy mx-auto mb-4" />
              <h3 className="font-medium text-airbnb-hof mb-2">No Active Subscription</h3>
              <p className="text-airbnb-foggy mb-4">
                You're currently on the free plan. Upgrade to unlock more features.
              </p>
              <Button
                className="bg-airbnb-rausch hover:bg-airbnb-rausch/90"
                onClick={() => handleUpgrade('pro')}
              >
                <Crown className="h-4 w-4 mr-2" />
                Upgrade to Pro
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment History */}
      <Card>
        <CardHeader>
          <CardTitle className="text-airbnb-hof">Payment History</CardTitle>
        </CardHeader>
        <CardContent>
          {paymentHistory.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Invoice</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paymentHistory.map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell>
                      {new Date(payment.date).toLocaleDateString()}
                    </TableCell>
                    <TableCell>{payment.description}</TableCell>
                    <TableCell>
                      ${payment.amount.toFixed(2)} {payment.currency}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(payment.status)}
                    </TableCell>
                    <TableCell>
                      {payment.invoiceUrl && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => window.open(payment.invoiceUrl, '_blank')}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-airbnb-foggy mx-auto mb-4" />
              <h3 className="font-medium text-airbnb-hof mb-2">No Payment History</h3>
              <p className="text-airbnb-foggy">
                Your payment history will appear here once you make your first payment.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
