"use client";

import { useState, useEffect } from "react";
import { useUser } from "@clerk/nextjs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  User,
  Mail,
  Shield,
  Camera,
  Check,
  X,
  Loader2,
  Lock,
  Eye,
  EyeOff
} from "lucide-react";
import { toast } from "sonner";

export function ProfileTab() {
  const { user, isLoaded } = useUser();
  const [isEditingProfile, setIsEditingProfile] = useState(false);
  const [isEditingPassword, setIsEditingPassword] = useState(false);
  const [isSavingProfile, setIsSavingProfile] = useState(false);
  const [isSavingPassword, setIsSavingPassword] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [profileData, setProfileData] = useState({
    firstName: "",
    lastName: "",
    email: "",
  });

  const [passwordData, setPasswordData] = useState({
    password: "",
    confirmPassword: "",
  });

  useEffect(() => {
    if (user) {
      setProfileData({
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        email: user.primaryEmailAddress?.emailAddress || "",
      });
    }
  }, [user]);

  const handleProfileSave = async () => {
    if (!user) return;

    setIsSavingProfile(true);
    try {
      const response = await fetch('/api/user', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(profileData),
      });

      if (!response.ok) {
        throw new Error('Failed to update profile');
      }

      toast.success("Profile updated successfully");
      setIsEditingProfile(false);
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile");
    } finally {
      setIsSavingProfile(false);
    }
  };

  const handleProfileCancel = () => {
    if (user) {
      setProfileData({
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        email: user.primaryEmailAddress?.emailAddress || "",
      });
    }
    setIsEditingProfile(false);
  };

  const handlePasswordSave = async () => {
    if (!user) return;

    if (passwordData.password !== passwordData.confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    if (passwordData.password.length < 8) {
      toast.error("Password must be at least 8 characters long");
      return;
    }

    setIsSavingPassword(true);
    try {
      const response = await fetch('/api/user', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password: passwordData.password }),
      });

      if (!response.ok) {
        throw new Error('Failed to update password');
      }

      toast.success("Password updated successfully");
      setIsEditingPassword(false);
      setPasswordData({ password: "", confirmPassword: "" });
    } catch (error) {
      console.error("Error updating password:", error);
      toast.error("Failed to update password");
    } finally {
      setIsSavingPassword(false);
    }
  };

  const handlePasswordCancel = () => {
    setPasswordData({ password: "", confirmPassword: "" });
    setIsEditingPassword(false);
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    try {
      // Upload image to Clerk
      await user.setProfileImage({ file });
      toast.success("Profile image updated successfully");
    } catch (error) {
      console.error("Error updating profile image:", error);
      toast.error("Failed to update profile image");
    }
  };

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-airbnb-rausch" />
      </div>
    );
  }

  if (!user) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <User className="h-12 w-12 text-airbnb-foggy mx-auto mb-4" />
          <p className="text-airbnb-foggy">Unable to load profile information</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* Profile Sidebar */}
      <div className="lg:col-span-1">
        <Card>
          <CardContent className="text-center py-6">
            <div className="relative inline-block mb-4">
              <Avatar className="h-24 w-24">
                <AvatarImage src={user.imageUrl} alt={user.fullName || "Profile"} />
                <AvatarFallback className="text-lg">
                  {user.firstName?.[0]}{user.lastName?.[0]}
                </AvatarFallback>
              </Avatar>
              <label className="absolute bottom-0 right-0 bg-airbnb-rausch text-white rounded-full p-2 cursor-pointer hover:bg-airbnb-rausch/90 transition-colors">
                <Camera className="h-4 w-4" />
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </label>
            </div>

            <h3 className="font-semibold text-airbnb-hof text-lg">
              {user.fullName || "User"}
            </h3>
            <p className="text-sm text-airbnb-foggy mb-4">
              {user.primaryEmailAddress?.emailAddress}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content - Two Forms */}
      <div className="lg:col-span-3 space-y-6">
        {/* Personal Information Form */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-airbnb-hof">Personal Information</CardTitle>
            {isEditingProfile && (
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleProfileCancel}
                  disabled={isSavingProfile}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleProfileSave}
                  disabled={isSavingProfile}
                  className="bg-airbnb-rausch hover:bg-airbnb-rausch/90"
                >
                  {isSavingProfile ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Check className="h-4 w-4 mr-2" />
                  )}
                  Save Changes
                </Button>
              </div>
            )}
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* First Name */}
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                {isEditingProfile ? (
                  <Input
                    id="firstName"
                    value={profileData.firstName}
                    onChange={(e) => setProfileData({ ...profileData, firstName: e.target.value })}
                    placeholder="Enter your first name"
                  />
                ) : (
                  <div className="p-3 bg-gray-50 rounded-md text-airbnb-hof">
                    {user.firstName || "Not provided"}
                  </div>
                )}
              </div>

              {/* Last Name */}
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                {isEditingProfile ? (
                  <Input
                    id="lastName"
                    value={profileData.lastName}
                    onChange={(e) => setProfileData({ ...profileData, lastName: e.target.value })}
                    placeholder="Enter your last name"
                  />
                ) : (
                  <div className="p-3 bg-gray-50 rounded-md text-airbnb-hof">
                    {user.lastName || "Not provided"}
                  </div>
                )}
              </div>
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email" className="flex items-center space-x-2">
                <span>Email</span>
                {user.primaryEmailAddress?.verification?.status === "verified" && (
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    <Shield className="h-3 w-3 mr-1" />
                    Verified
                  </Badge>
                )}
              </Label>
              {isEditingProfile ? (
                <Input
                  id="email"
                  type="email"
                  value={profileData.email}
                  onChange={(e) => setProfileData({ ...profileData, email: e.target.value })}
                  placeholder="Enter your email"
                />
              ) : (
                <div className="p-3 bg-gray-50 rounded-md text-airbnb-hof flex items-center justify-between">
                  <span>{user.primaryEmailAddress?.emailAddress || "Not provided"}</span>
                  {user.primaryEmailAddress?.verification?.status === "verified" && (
                    <Check className="h-4 w-4 text-green-600" />
                  )}
                </div>
              )}
            </div>

            {/* Edit Button */}
            {!isEditingProfile && (
              <div className="pt-4">
                <Button
                  onClick={() => setIsEditingProfile(true)}
                  className="bg-airbnb-rausch hover:bg-airbnb-rausch/90"
                >
                  <User className="h-4 w-4 mr-2" />
                  Edit Profile
                </Button>
              </div>
            )}

            {/* Account Information */}
            <div className="pt-6 border-t">
              <h4 className="font-medium text-airbnb-hof mb-4">Account Information</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label>Member Since</Label>
                  <div className="p-3 bg-gray-50 rounded-md text-airbnb-hof">
                    {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : "Unknown"}
                  </div>
                </div>
                <div>
                  <Label>Account ID</Label>
                  <div className="p-3 bg-gray-50 rounded-md text-airbnb-hof font-mono text-sm">
                    {user.id}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Password Form */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-airbnb-hof">Password & Security</CardTitle>
            {isEditingPassword && (
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePasswordCancel}
                  disabled={isSavingPassword}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handlePasswordSave}
                  disabled={isSavingPassword}
                  className="bg-airbnb-rausch hover:bg-airbnb-rausch/90"
                >
                  {isSavingPassword ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Check className="h-4 w-4 mr-2" />
                  )}
                  Update Password
                </Button>
              </div>
            )}
          </CardHeader>
          <CardContent className="space-y-6">
            {isEditingPassword ? (
              <div className="space-y-4">
                {/* New Password */}
                <div className="space-y-2">
                  <Label htmlFor="password">New Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={passwordData.password}
                      onChange={(e) => setPasswordData({ ...passwordData, password: e.target.value })}
                      placeholder="Enter new password (min 8 characters)"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-airbnb-foggy hover:text-airbnb-hof"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                {/* Confirm Password */}
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      value={passwordData.confirmPassword}
                      onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
                      placeholder="Confirm new password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-airbnb-foggy hover:text-airbnb-hof"
                    >
                      {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                {/* Password Requirements */}
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h5 className="font-medium text-blue-800 mb-2">Password Requirements:</h5>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${passwordData.password.length >= 8 ? 'bg-green-500' : 'bg-gray-300'}`} />
                      <span>At least 8 characters long</span>
                    </li>
                    <li className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${passwordData.password === passwordData.confirmPassword && passwordData.password.length > 0 ? 'bg-green-500' : 'bg-gray-300'}`} />
                      <span>Passwords match</span>
                    </li>
                  </ul>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Current Password</Label>
                  <div className="p-3 bg-gray-50 rounded-md text-airbnb-hof">
                    ••••••••••••
                  </div>
                </div>

                <div className="pt-4">
                  <Button
                    onClick={() => setIsEditingPassword(true)}
                    className="bg-airbnb-rausch hover:bg-airbnb-rausch/90"
                  >
                    <Lock className="h-4 w-4 mr-2" />
                    Change Password
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
