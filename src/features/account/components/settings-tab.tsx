"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Download, 
  Trash2, 
  AlertTriangle, 
  Shield, 
  FileText,
  Database,
  Lock,
  Eye,
  EyeOff,
  Bell,
  Mail
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";

export function SettingsTab() {
  const [isDownloading, setIsDownloading] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState("");
  const [preferences, setPreferences] = useState({
    emailNotifications: true,
    marketingEmails: false,
    dataProcessing: true,
    analytics: true,
  });

  const handleDownloadData = async () => {
    setIsDownloading(true);
    try {
      // Simulate API call to generate data export
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation, this would call your API to generate a data export
      const response = await fetch('/api/account/export-data', {
        method: 'POST',
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `guestfile-data-export-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        toast.success("Data export downloaded successfully");
      } else {
        throw new Error("Failed to export data");
      }
    } catch (error) {
      console.error('Error downloading data:', error);
      toast.error("Failed to download data export");
    } finally {
      setIsDownloading(false);
    }
  };

  const handleDeleteAccount = async () => {
    if (deleteConfirmation !== "DELETE") {
      toast.error("Please type DELETE to confirm account deletion");
      return;
    }

    try {
      // In a real implementation, this would call your API to delete the account
      const response = await fetch('/api/account/delete', {
        method: 'DELETE',
      });
      
      if (response.ok) {
        toast.success("Account deletion initiated. You will be logged out shortly.");
        // Redirect to sign out
        setTimeout(() => {
          window.location.href = '/sign-out';
        }, 2000);
      } else {
        throw new Error("Failed to delete account");
      }
    } catch (error) {
      console.error('Error deleting account:', error);
      toast.error("Failed to delete account");
    }
  };

  const handlePreferenceChange = (key: string, value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
    
    // In a real implementation, save to API
    toast.success("Preferences updated");
  };

  return (
    <div className="space-y-6">
      {/* Privacy & Data */}
      <Card>
        <CardHeader>
          <CardTitle className="text-airbnb-hof flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Privacy & Data</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Data Export */}
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <h4 className="font-medium text-airbnb-hof">Download Your Data</h4>
              <p className="text-sm text-airbnb-foggy">
                Export all your personal data stored in our system. This includes your profile information, 
                guest records, reviews, and organization data in compliance with LGPD and GDPR regulations.
              </p>
              <div className="flex items-center space-x-2 mt-2">
                <Badge variant="outline" className="text-xs">
                  <FileText className="h-3 w-3 mr-1" />
                  LGPD Compliant
                </Badge>
                <Badge variant="outline" className="text-xs">
                  <Database className="h-3 w-3 mr-1" />
                  GDPR Compliant
                </Badge>
              </div>
            </div>
            <Button
              onClick={handleDownloadData}
              disabled={isDownloading}
              className="bg-airbnb-rausch hover:bg-airbnb-rausch/90"
            >
              {isDownloading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Preparing...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Download Data
                </>
              )}
            </Button>
          </div>

          <Separator />

          {/* Privacy Preferences */}
          <div className="space-y-4">
            <h4 className="font-medium text-airbnb-hof">Privacy Preferences</h4>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="email-notifications">Email Notifications</Label>
                  <p className="text-sm text-airbnb-foggy">
                    Receive important updates about your account and bookings
                  </p>
                </div>
                <Switch
                  id="email-notifications"
                  checked={preferences.emailNotifications}
                  onCheckedChange={(checked) => handlePreferenceChange('emailNotifications', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="marketing-emails">Marketing Communications</Label>
                  <p className="text-sm text-airbnb-foggy">
                    Receive promotional emails and product updates
                  </p>
                </div>
                <Switch
                  id="marketing-emails"
                  checked={preferences.marketingEmails}
                  onCheckedChange={(checked) => handlePreferenceChange('marketingEmails', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="data-processing">Data Processing</Label>
                  <p className="text-sm text-airbnb-foggy">
                    Allow processing of your data for service improvement
                  </p>
                </div>
                <Switch
                  id="data-processing"
                  checked={preferences.dataProcessing}
                  onCheckedChange={(checked) => handlePreferenceChange('dataProcessing', checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="analytics">Analytics & Performance</Label>
                  <p className="text-sm text-airbnb-foggy">
                    Help us improve our service with anonymous usage data
                  </p>
                </div>
                <Switch
                  id="analytics"
                  checked={preferences.analytics}
                  onCheckedChange={(checked) => handlePreferenceChange('analytics', checked)}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Rights */}
      <Card>
        <CardHeader>
          <CardTitle className="text-airbnb-hof flex items-center space-x-2">
            <Lock className="h-5 w-5" />
            <span>Your Data Rights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-airbnb-foggy">
              Under LGPD (Brazilian General Data Protection Law) and GDPR (General Data Protection Regulation), 
              you have the following rights regarding your personal data:
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h5 className="font-medium text-airbnb-hof">Right to Access</h5>
                <p className="text-sm text-airbnb-foggy">
                  Request access to your personal data we process
                </p>
              </div>
              
              <div className="space-y-2">
                <h5 className="font-medium text-airbnb-hof">Right to Rectification</h5>
                <p className="text-sm text-airbnb-foggy">
                  Request correction of inaccurate personal data
                </p>
              </div>
              
              <div className="space-y-2">
                <h5 className="font-medium text-airbnb-hof">Right to Erasure</h5>
                <p className="text-sm text-airbnb-foggy">
                  Request deletion of your personal data
                </p>
              </div>
              
              <div className="space-y-2">
                <h5 className="font-medium text-airbnb-hof">Right to Portability</h5>
                <p className="text-sm text-airbnb-foggy">
                  Receive your data in a structured format
                </p>
              </div>
            </div>
            
            <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Need help with your data rights?</strong> Contact our Data Protection Officer at 
                <a href="mailto:<EMAIL>" className="underline ml-1"><EMAIL></a>
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Danger Zone */}
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="text-red-600 flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Danger Zone</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <h4 className="font-medium text-red-600">Delete Account</h4>
                <p className="text-sm text-airbnb-foggy">
                  Permanently delete your account and all associated data. This action cannot be undone.
                  All your guests, reviews, properties, and organizations will be permanently removed.
                </p>
                <div className="mt-2">
                  <Badge variant="destructive" className="text-xs">
                    <Trash2 className="h-3 w-3 mr-1" />
                    Irreversible Action
                  </Badge>
                </div>
              </div>
              
              <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <DialogTrigger asChild>
                  <Button variant="destructive">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Account
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle className="text-red-600">Delete Account</DialogTitle>
                    <DialogDescription>
                      This action will permanently delete your account and all associated data. 
                      This includes all guests, reviews, properties, and organizations you've created.
                      <br /><br />
                      <strong>This action cannot be undone.</strong>
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="space-y-4">
                    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-sm text-red-800">
                        To confirm deletion, please type <strong>DELETE</strong> in the field below:
                      </p>
                    </div>
                    
                    <input
                      type="text"
                      placeholder="Type DELETE to confirm"
                      value={deleteConfirmation}
                      onChange={(e) => setDeleteConfirmation(e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    />
                  </div>
                  
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowDeleteDialog(false);
                        setDeleteConfirmation("");
                      }}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={handleDeleteAccount}
                      disabled={deleteConfirmation !== "DELETE"}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Account
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
