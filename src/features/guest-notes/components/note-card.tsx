"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  CheckCircle, 
  XCircle, 
  MessageSquare, 
  Calendar,
  AlertTriangle,
  Shield,
  ThumbsUp,
  ThumbsDown
} from "lucide-react";
import { GuestNote, NoteVerification } from "../types";

interface NoteCardProps {
  note: GuestNote;
  onVerify: (noteId: string, comment: string, isConfirming: boolean) => Promise<void>;
  currentUserId: string;
}

export function NoteCard({ note, onVerify, currentUserId }: NoteCardProps) {
  const [showVerifyForm, setShowVerifyForm] = useState(false);
  const [verifyComment, setVerifyComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleVerify = async (isConfirming: boolean) => {
    if (!verifyComment.trim()) return;

    setIsSubmitting(true);
    try {
      await onVerify(note.id, verifyComment, isConfirming);
      setVerifyComment("");
      setShowVerifyForm(false);
    } catch (error) {
      console.error('Error verifying note:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'cleanliness': return '🧹';
      case 'behavior': return '👤';
      case 'damage': return '⚠️';
      default: return '📝';
    }
  };

  const hasUserVerified = note.verifications?.some(v => v.verifierId === currentUserId);
  const isAuthor = note.authorId === currentUserId;

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={note.author?.imageUrl} />
              <AvatarFallback>
                {note.author?.firstName?.[0]}{note.author?.lastName?.[0]}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center space-x-2">
                <h4 className="font-semibold text-airbnb-hof">{note.title}</h4>
                <span className="text-lg">{getCategoryIcon(note.category)}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-airbnb-foggy">
                <span>{note.author?.firstName} {note.author?.lastName}</span>
                <span>•</span>
                <Calendar className="h-3 w-3" />
                <span>{new Date(note.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge className={getSeverityColor(note.severity)}>
              {note.severity.toUpperCase()}
            </Badge>
            {note.isVerified && (
              <Badge className="bg-green-100 text-green-800 border-green-200">
                <Shield className="h-3 w-3 mr-1" />
                Verified
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <p className="text-airbnb-foggy">{note.content}</p>

        {/* Verification Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <ThumbsUp className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">{note.verificationCount}</span>
              <span className="text-sm text-airbnb-foggy">confirmations</span>
            </div>
            {note.verifications && note.verifications.length > 0 && (
              <div className="flex items-center space-x-1">
                <MessageSquare className="h-4 w-4 text-airbnb-foggy" />
                <span className="text-sm text-airbnb-foggy">
                  {note.verifications.length} {note.verifications.length === 1 ? 'comment' : 'comments'}
                </span>
              </div>
            )}
          </div>

          {!isAuthor && !hasUserVerified && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowVerifyForm(!showVerifyForm)}
              className="text-airbnb-rausch border-airbnb-rausch hover:bg-airbnb-rausch hover:text-white"
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Verify
            </Button>
          )}
        </div>

        {/* Verification Form */}
        {showVerifyForm && (
          <div className="p-4 border rounded-lg bg-white space-y-3">
            <h5 className="font-medium text-airbnb-hof">Share your experience</h5>
            <Textarea
              placeholder="Describe your experience with this guest to help verify this note..."
              value={verifyComment}
              onChange={(e) => setVerifyComment(e.target.value)}
              rows={3}
            />
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                onClick={() => handleVerify(true)}
                disabled={!verifyComment.trim() || isSubmitting}
                className="bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Confirm
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleVerify(false)}
                disabled={!verifyComment.trim() || isSubmitting}
                className="text-red-600 border-red-600 hover:bg-red-600 hover:text-white"
              >
                <XCircle className="h-4 w-4 mr-2" />
                Dispute
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowVerifyForm(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* Verification Comments */}
        {note.verifications && note.verifications.length > 0 && (
          <div className="space-y-3">
            <h5 className="font-medium text-airbnb-hof">Verifications</h5>
            {note.verifications.map((verification) => (
              <div key={verification.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={verification.verifier?.imageUrl} />
                  <AvatarFallback className="text-xs">
                    {verification.verifier?.firstName?.[0]}{verification.verifier?.lastName?.[0]}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">
                      {verification.verifier?.firstName} {verification.verifier?.lastName}
                    </span>
                    {verification.isConfirming ? (
                      <Badge className="bg-green-100 text-green-800 border-green-200">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Confirmed
                      </Badge>
                    ) : (
                      <Badge className="bg-red-100 text-red-800 border-red-200">
                        <XCircle className="h-3 w-3 mr-1" />
                        Disputed
                      </Badge>
                    )}
                    <span className="text-xs text-airbnb-foggy">
                      {new Date(verification.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                  <p className="text-sm text-airbnb-foggy mt-1">{verification.comment}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
