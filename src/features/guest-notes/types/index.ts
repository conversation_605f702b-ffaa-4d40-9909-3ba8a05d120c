export interface GuestNote {
  id: string;
  guestId: string;
  authorId: string;
  organizationId: string;
  title: string;
  content: string;
  category: 'cleanliness' | 'behavior' | 'damage' | 'general';
  severity: 'low' | 'medium' | 'high';
  isVerified: boolean;
  verificationCount: number;
  createdAt: Date;
  updatedAt: Date;
  // Relations
  author?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
  };
  verifications?: NoteVerification[];
}

export interface NoteVerification {
  id: string;
  noteId: string;
  verifierId: string;
  organizationId: string;
  comment: string;
  isConfirming: boolean;
  createdAt: Date;
  // Relations
  verifier?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
  };
}

export interface CreateNoteData {
  title: string;
  content: string;
  category: 'cleanliness' | 'behavior' | 'damage' | 'general';
  severity: 'low' | 'medium' | 'high';
}

export interface CreateVerificationData {
  comment: string;
  isConfirming: boolean;
}

export interface NoteWithVerifications extends GuestNote {
  verifications: NoteVerification[];
}
