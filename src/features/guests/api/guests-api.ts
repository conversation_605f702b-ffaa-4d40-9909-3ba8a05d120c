import { PaginationParams, PaginatedResponse } from "@/shared/types/pagination";
import { Guest } from "../types";

export async function fetchGuests(params: PaginationParams): Promise<PaginatedResponse<Guest>> {
  const searchParams = new URLSearchParams();
  
  searchParams.append('page', params.page.toString());
  searchParams.append('limit', params.limit.toString());
  
  if (params.search) {
    searchParams.append('search', params.search);
  }
  
  if (params.sortBy) {
    searchParams.append('sortBy', params.sortBy);
  }
  
  if (params.sortOrder) {
    searchParams.append('sortOrder', params.sortOrder);
  }

  const response = await fetch(`/api/guests?${searchParams}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch guests');
  }

  return response.json();
}
