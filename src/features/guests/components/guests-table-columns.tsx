"use client";

import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, Mail, Phone, Eye, Edit, MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { Guest } from "../types";

export const guestsTableColumns: ColumnDef<Guest>[] = [
  {
    accessorKey: "firstName",
    header: "Guest",
    cell: ({ row }) => {
      const guest = row.original;
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={guest.imageUrl} />
            <AvatarFallback>
              {guest.firstName[0]}{guest.lastName[0]}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium text-airbnb-hof">
              {guest.firstName} {guest.lastName}
            </div>
            {guest.nationality && (
              <div className="text-sm text-airbnb-foggy">
                {guest.nationality}
              </div>
            )}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "email",
    header: "Contact",
    cell: ({ row }) => {
      const guest = row.original;
      return (
        <div className="space-y-1">
          {guest.email && (
            <div className="flex items-center space-x-1 text-sm">
              <Mail className="h-3 w-3 text-airbnb-foggy" />
              <span className="text-airbnb-foggy">{guest.email}</span>
            </div>
          )}
          {guest.phone && (
            <div className="flex items-center space-x-1 text-sm">
              <Phone className="h-3 w-3 text-airbnb-foggy" />
              <span className="text-airbnb-foggy">{guest.phone}</span>
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "averageRating",
    header: "Rating",
    cell: ({ row }) => {
      const guest = row.original;
      const rating = Number(guest.averageRating || 0);
      const reviews = guest.totalReviews || 0;
      
      return (
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <Star className="h-4 w-4 text-yellow-400 fill-current" />
            <span className="font-medium">{rating.toFixed(1)}</span>
          </div>
          <Badge variant="secondary" className="text-xs">
            {reviews} review{reviews !== 1 ? 's' : ''}
          </Badge>
        </div>
      );
    },
  },
  {
    accessorKey: "documentType",
    header: "Document",
    cell: ({ row }) => {
      const guest = row.original;
      if (!guest.documentType) return <span className="text-airbnb-foggy">-</span>;
      
      const documentLabels = {
        passport: "Passport",
        id: "ID Card",
        driver_license: "Driver's License",
      };
      
      return (
        <div className="space-y-1">
          <Badge variant="outline" className="text-xs">
            {documentLabels[guest.documentType]}
          </Badge>
          {guest.documentNumber && (
            <div className="text-xs text-airbnb-foggy">
              {guest.documentNumber}
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: "Joined",
    cell: ({ row }) => {
      const guest = row.original;
      return (
        <div className="text-sm text-airbnb-foggy">
          {new Date(guest.createdAt).toLocaleDateString()}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const guest = row.original;
      
      return (
        <div className="flex items-center space-x-2">
          <Link href={`/dashboard/guests/${guest.id}`}>
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/guests/${guest.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/guests/${guest.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Guest
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  },
];
