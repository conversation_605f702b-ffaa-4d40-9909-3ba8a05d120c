export interface Guest {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  dateOfBirth?: Date;
  nationality?: string;
  documentType?: 'passport' | 'id' | 'driver_license';
  documentNumber?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  notes?: string;
  imageUrl?: string;
  averageRating: number | string | null;
  totalReviews: number | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface Review {
  id: string;
  guestId: string;
  organizationId: string;
  reviewerId: string;
  rating: number; // 1-5
  title?: string;
  comment?: string;
  stayDates?: {
    checkIn: Date;
    checkOut: Date;
  };
  propertyType?: 'apartment' | 'house' | 'room';
  wouldRecommend?: boolean;
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
  reviewer?: {
    firstName?: string;
    lastName?: string;
    email: string;
  };
  organization?: {
    name: string;
  };
}

export interface CreateGuestData {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  dateOfBirth?: Date;
  nationality?: string;
  documentType?: 'passport' | 'id' | 'driver_license';
  documentNumber?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  notes?: string;
}

export interface CreateReviewData {
  guestId: string;
  rating: number;
  title?: string;
  comment?: string;
  stayDates?: {
    checkIn: Date;
    checkOut: Date;
  };
  propertyType?: 'apartment' | 'house' | 'room';
  wouldRecommend?: boolean;
  tags?: string[];
}
