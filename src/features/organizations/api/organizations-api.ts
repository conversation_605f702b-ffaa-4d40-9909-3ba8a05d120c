import { PaginationParams, PaginatedResponse } from "@/shared/types/pagination";

interface Organization {
  id: string;
  name: string;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  zipCode?: string;
  phone?: string;
  email?: string;
  website?: string;
  createdAt: Date;
  updatedAt: Date;
}

export async function fetchOrganizations(params: PaginationParams): Promise<PaginatedResponse<Organization>> {
  const searchParams = new URLSearchParams();
  
  searchParams.append('page', params.page.toString());
  searchParams.append('limit', params.limit.toString());
  
  if (params.search) {
    searchParams.append('search', params.search);
  }
  
  if (params.sortBy) {
    searchParams.append('sortBy', params.sortBy);
  }
  
  if (params.sortOrder) {
    searchParams.append('sortOrder', params.sortOrder);
  }

  const response = await fetch(`/api/organizations?${searchParams}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch organizations');
  }

  return response.json();
}
