"use client";

import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Building2, MapPin, Globe, Eye, Edit, MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";

interface Organization {
  id: string;
  name: string;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  zipCode?: string;
  phone?: string;
  email?: string;
  website?: string;
  createdAt: Date;
  updatedAt: Date;
}

export const organizationsTableColumns: ColumnDef<Organization>[] = [
  {
    accessorKey: "name",
    header: "Organization",
    cell: ({ row }) => {
      const org = row.original;
      return (
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 bg-airbnb-babu rounded-lg flex items-center justify-center">
            <Building2 className="h-5 w-5 text-white" />
          </div>
          <div>
            <div className="font-medium text-airbnb-hof">{org.name}</div>
            {org.description && (
              <div className="text-sm text-airbnb-foggy line-clamp-1">
                {org.description}
              </div>
            )}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "location",
    header: "Location",
    cell: ({ row }) => {
      const org = row.original;
      const location = [org.city, org.state, org.country].filter(Boolean).join(", ");
      
      return (
        <div className="space-y-1">
          {location && (
            <div className="flex items-center space-x-1 text-sm">
              <MapPin className="h-3 w-3 text-airbnb-foggy" />
              <span className="text-airbnb-foggy">{location}</span>
            </div>
          )}
          {org.address && (
            <div className="text-xs text-airbnb-foggy line-clamp-1">
              {org.address}
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "contact",
    header: "Contact",
    cell: ({ row }) => {
      const org = row.original;
      return (
        <div className="space-y-1">
          {org.email && (
            <div className="text-sm text-airbnb-foggy">{org.email}</div>
          )}
          {org.phone && (
            <div className="text-sm text-airbnb-foggy">{org.phone}</div>
          )}
          {org.website && (
            <div className="flex items-center space-x-1 text-sm">
              <Globe className="h-3 w-3 text-airbnb-foggy" />
              <a 
                href={org.website} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-airbnb-rausch hover:underline"
              >
                Website
              </a>
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created",
    cell: ({ row }) => {
      const org = row.original;
      return (
        <div className="text-sm text-airbnb-foggy">
          {new Date(org.createdAt).toLocaleDateString()}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const org = row.original;
      
      return (
        <div className="flex items-center space-x-2">
          <Link href={`/dashboard/organizations/${org.id}`}>
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/organizations/${org.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/organizations/${org.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Organization
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  },
];
