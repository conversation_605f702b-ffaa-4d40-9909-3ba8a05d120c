import { PaginationParams, PaginatedResponse } from "@/shared/types/pagination";

interface Property {
  id: string;
  organizationId: string;
  name: string;
  type: string;
  category: string;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  bedrooms: number;
  bathrooms: number;
  maxGuests: number;
  area?: number;
  areaUnit?: string;
  amenities: string[];
  images: string[];
  isActive: boolean;
  isListed: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export async function fetchProperties(params: PaginationParams): Promise<PaginatedResponse<Property>> {
  const searchParams = new URLSearchParams();
  
  searchParams.append('page', params.page.toString());
  searchParams.append('limit', params.limit.toString());
  
  if (params.search) {
    searchParams.append('search', params.search);
  }
  
  if (params.sortBy) {
    searchParams.append('sortBy', params.sortBy);
  }
  
  if (params.sortOrder) {
    searchParams.append('sortOrder', params.sortOrder);
  }

  const response = await fetch(`/api/properties?${searchParams}`);
  
  if (!response.ok) {
    throw new Error('Failed to fetch properties');
  }

  return response.json();
}
