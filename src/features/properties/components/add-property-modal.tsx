"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CreatePropertyData, PropertyType, PropertyCategory, AreaUnit } from "../types";
import { PROPERTY_CATEGORIES, getPropertyTypesByCategory } from "../constants/property-types";
import { COMMON_AMENITIES } from "../types";
import { ImageUpload } from "@/components/ui/image-upload";

interface AddPropertyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreatePropertyData) => Promise<void>;
  organizationId: string;
}

export function AddPropertyModal({ isOpen, onClose, onSubmit, organizationId }: AddPropertyModalProps) {
  const [formData, setFormData] = useState<CreatePropertyData>({
    name: "",
    type: "house" as PropertyType,
    description: "",
    address: "",
    city: "",
    state: "",
    country: "",
    postalCode: "",
    bedrooms: 1,
    bathrooms: 1,
    maxGuests: 2,
    area: undefined,
    areaUnit: "sqft" as AreaUnit,
    amenities: [],
    images: [],
  });
  const [selectedCategory, setSelectedCategory] = useState<PropertyCategory>("houses");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.address || !formData.city || !formData.country) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit({
        ...formData,
        category: selectedCategory,
      });

      // Reset form
      setFormData({
        name: "",
        type: "house" as PropertyType,
        description: "",
        address: "",
        city: "",
        state: "",
        country: "",
        postalCode: "",
        bedrooms: 1,
        bathrooms: 1,
        maxGuests: 2,
        area: undefined,
        areaUnit: "sqft" as AreaUnit,
        amenities: [],
        images: [],
      });
      setSelectedCategory("houses");
      onClose();
    } catch (error) {
      console.error('Error submitting property:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  const handleAmenityToggle = (amenity: string) => {
    const currentAmenities = formData.amenities || [];
    const newAmenities = currentAmenities.includes(amenity)
      ? currentAmenities.filter(a => a !== amenity)
      : [...currentAmenities, amenity];
    setFormData({ ...formData, amenities: newAmenities });
  };

  const handleImagesChange = (imageFiles: any[]) => {
    const files = imageFiles.map(img => img.file);
    setFormData({ ...formData, images: files });
  };

  const propertyTypes = getPropertyTypesByCategory(selectedCategory);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-airbnb-hof">Add New Property</DialogTitle>
          <DialogDescription>
            Create a new property listing for your organization.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-airbnb-hof">Basic Information</h3>

            <div className="space-y-2">
              <Label htmlFor="name">Property Name *</Label>
              <Input
                id="name"
                placeholder="e.g., Cozy Downtown Apartment"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </div>

            {/* Property Type Selection */}
            <div className="space-y-2">
              <Label>Property Category *</Label>
              <Select
                value={selectedCategory}
                onValueChange={(value: PropertyCategory) => {
                  setSelectedCategory(value);
                  const firstType = getPropertyTypesByCategory(value)[0];
                  if (firstType) {
                    setFormData({ ...formData, type: firstType.type });
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(PROPERTY_CATEGORIES).map(([key, category]) => (
                    <SelectItem key={key} value={key}>
                      <div className="flex items-center space-x-2">
                        <span>{category.icon}</span>
                        <span>{category.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Property Type *</Label>
              <Select
                value={formData.type}
                onValueChange={(value: PropertyType) => setFormData({ ...formData, type: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select property type" />
                </SelectTrigger>
                <SelectContent>
                  {propertyTypes.map((type) => (
                    <SelectItem key={type.type} value={type.type}>
                      <div className="flex items-center space-x-2">
                        <span>{type.icon}</span>
                        <div>
                          <div>{type.label}</div>
                          <div className="text-xs text-gray-500">{type.description}</div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe your property..."
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={3}
              />
            </div>
          </div>

          {/* Location */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-airbnb-hof">Location</h3>

            <div className="space-y-2">
              <Label htmlFor="address">Address *</Label>
              <Input
                id="address"
                placeholder="Street address"
                value={formData.address}
                onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  placeholder="City"
                  value={formData.city}
                  onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="state">State/Province</Label>
                <Input
                  id="state"
                  placeholder="State or Province"
                  value={formData.state}
                  onChange={(e) => setFormData({ ...formData, state: e.target.value })}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="country">Country *</Label>
                <Input
                  id="country"
                  placeholder="Country"
                  value={formData.country}
                  onChange={(e) => setFormData({ ...formData, country: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="postalCode">Postal Code</Label>
                <Input
                  id="postalCode"
                  placeholder="Postal/ZIP code"
                  value={formData.postalCode}
                  onChange={(e) => setFormData({ ...formData, postalCode: e.target.value })}
                />
              </div>
            </div>
          </div>

          {/* Property Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-airbnb-hof">Property Details</h3>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="bedrooms">Bedrooms *</Label>
                <Input
                  id="bedrooms"
                  type="number"
                  min="0"
                  value={formData.bedrooms}
                  onChange={(e) => setFormData({ ...formData, bedrooms: parseInt(e.target.value) || 0 })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="bathrooms">Bathrooms *</Label>
                <Input
                  id="bathrooms"
                  type="number"
                  step="0.5"
                  min="0"
                  value={formData.bathrooms}
                  onChange={(e) => setFormData({ ...formData, bathrooms: parseFloat(e.target.value) || 0 })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxGuests">Max Guests *</Label>
                <Input
                  id="maxGuests"
                  type="number"
                  min="1"
                  value={formData.maxGuests}
                  onChange={(e) => setFormData({ ...formData, maxGuests: parseInt(e.target.value) || 1 })}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="area">Floor Area</Label>
              <div className="flex space-x-2">
                <Input
                  id="area"
                  type="number"
                  min="0"
                  placeholder="Optional"
                  value={formData.area || ""}
                  onChange={(e) => setFormData({
                    ...formData,
                    area: e.target.value ? parseInt(e.target.value) : undefined
                  })}
                  className="flex-1"
                />
                <Select
                  value={formData.areaUnit}
                  onValueChange={(value: AreaUnit) => setFormData({ ...formData, areaUnit: value })}
                >
                  <SelectTrigger className="w-24">
                    <SelectValue>
                      {formData.areaUnit === 'sqm' ? 'm²' : 'ft²'}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="sqft">ft²</SelectItem>
                    <SelectItem value="sqm">m²</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Images */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-airbnb-hof">Property Photos</h3>
            <p className="text-sm text-airbnb-foggy">
              Add up to 20 photos to showcase your property (max 5MB each)
            </p>
            <ImageUpload
              maxFiles={20}
              maxSizePerFile={5}
              onImagesChange={handleImagesChange}
              variant="review"
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-airbnb-rausch hover:bg-airbnb-rausch/90"
              disabled={isSubmitting || !formData.name || !formData.address || !formData.city || !formData.country}
            >
              {isSubmitting ? "Creating..." : "Create Property"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
