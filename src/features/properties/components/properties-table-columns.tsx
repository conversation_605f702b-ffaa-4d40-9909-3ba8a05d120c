"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Home, MapPin, Users, Bed, Bath, Eye, Edit, MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";

interface Property {
  id: string;
  organizationId: string;
  name: string;
  type: string;
  category: string;
  description?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  bedrooms: number;
  bathrooms: number;
  maxGuests: number;
  area?: number;
  areaUnit?: string;
  amenities: string[];
  images: string[];
  isActive: boolean;
  isListed: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export const propertiesTableColumns: ColumnDef<Property>[] = [
  {
    accessorKey: "name",
    header: "Property",
    cell: ({ row }) => {
      const property = row.original;
      return (
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 bg-airbnb-babu rounded-lg flex items-center justify-center">
            <Home className="h-5 w-5 text-white" />
          </div>
          <div>
            <div className="font-medium text-airbnb-hof">{property.name}</div>
            <div className="text-sm text-airbnb-foggy">
              {property.type} • {property.category}
            </div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "location",
    header: "Location",
    cell: ({ row }) => {
      const property = row.original;
      const location = [property.city, property.state, property.country].filter(Boolean).join(", ");
      
      return (
        <div className="space-y-1">
          {location && (
            <div className="flex items-center space-x-1 text-sm">
              <MapPin className="h-3 w-3 text-airbnb-foggy" />
              <span className="text-airbnb-foggy">{location}</span>
            </div>
          )}
          {property.address && (
            <div className="text-xs text-airbnb-foggy line-clamp-1">
              {property.address}
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "details",
    header: "Details",
    cell: ({ row }) => {
      const property = row.original;
      return (
        <div className="space-y-1">
          <div className="flex items-center space-x-3 text-sm">
            <div className="flex items-center space-x-1">
              <Bed className="h-3 w-3 text-airbnb-foggy" />
              <span className="text-airbnb-foggy">{property.bedrooms}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Bath className="h-3 w-3 text-airbnb-foggy" />
              <span className="text-airbnb-foggy">{property.bathrooms}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Users className="h-3 w-3 text-airbnb-foggy" />
              <span className="text-airbnb-foggy">{property.maxGuests}</span>
            </div>
          </div>
          {property.area && (
            <div className="text-xs text-airbnb-foggy">
              {property.area} {property.areaUnit || 'sq ft'}
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const property = row.original;
      return (
        <div className="space-y-1">
          <Badge
            variant={property.isActive ? "default" : "secondary"}
            className={property.isActive ? "bg-green-100 text-green-800 border-green-200" : ""}
          >
            {property.isActive ? "Active" : "Inactive"}
          </Badge>
          {property.isListed && (
            <Badge
              variant="outline"
              className="bg-blue-100 text-blue-800 border-blue-200"
            >
              Listed
            </Badge>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "amenities",
    header: "Amenities",
    cell: ({ row }) => {
      const property = row.original;
      const amenityCount = property.amenities?.length || 0;
      
      return (
        <div className="text-sm text-airbnb-foggy">
          {amenityCount > 0 ? `${amenityCount} amenities` : "No amenities"}
        </div>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created",
    cell: ({ row }) => {
      const property = row.original;
      return (
        <div className="text-sm text-airbnb-foggy">
          {new Date(property.createdAt).toLocaleDateString()}
        </div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const property = row.original;
      
      return (
        <div className="flex items-center space-x-2">
          <Link href={`/dashboard/properties/${property.id}`}>
            <Button variant="ghost" size="sm">
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/properties/${property.id}`}>
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/dashboard/properties/${property.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Property
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  },
];
