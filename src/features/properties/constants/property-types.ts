import { PropertyTypeInfo, PropertyType, PropertyCategory } from '../types';

export const PROPERTY_TYPES: Record<PropertyType, PropertyTypeInfo> = {
  // Houses
  house: {
    type: 'house',
    category: 'houses',
    label: 'House',
    description: 'A residential building for one family',
    icon: '🏠'
  },
  cabin: {
    type: 'cabin',
    category: 'houses',
    label: 'Cabin',
    description: 'A small wooden house in a rural area',
    icon: '🏘️'
  },
  villa: {
    type: 'villa',
    category: 'houses',
    label: 'Villa',
    description: 'A large and luxurious house',
    icon: '🏛️'
  },
  cottage: {
    type: 'cottage',
    category: 'houses',
    label: 'Cottage',
    description: 'A small house, typically in a rural area',
    icon: '🏡'
  },
  bungalow: {
    type: 'bungalow',
    category: 'houses',
    label: 'Bungalow',
    description: 'A low house having only one storey',
    icon: '🏘️'
  },
  chalet: {
    type: 'chalet',
    category: 'houses',
    label: 'Chalet',
    description: 'A wooden house with overhanging eaves',
    icon: '🏔️'
  },
  cycladic_house: {
    type: 'cycladic_house',
    category: 'houses',
    label: 'Cycladic House',
    description: 'Traditional Greek island architecture',
    icon: '🏛️'
  },
  dammuso: {
    type: 'dammuso',
    category: 'houses',
    label: 'Dammuso',
    description: 'Traditional stone house from Pantelleria',
    icon: '🏠'
  },
  dome_house: {
    type: 'dome_house',
    category: 'houses',
    label: 'Dome House',
    description: 'House with a dome-shaped roof',
    icon: '🏠'
  },
  earth_house: {
    type: 'earth_house',
    category: 'houses',
    label: 'Earth House',
    description: 'House built into or covered with earth',
    icon: '🏠'
  },
  farm_stay: {
    type: 'farm_stay',
    category: 'houses',
    label: 'Farm Stay',
    description: 'Accommodation on a working farm',
    icon: '🚜'
  },
  houseboat: {
    type: 'houseboat',
    category: 'houses',
    label: 'Houseboat',
    description: 'A boat that has been designed for use as a home',
    icon: '🛥️'
  },
  hut: {
    type: 'hut',
    category: 'houses',
    label: 'Hut',
    description: 'A small simple dwelling',
    icon: '🛖'
  },
  lighthouse: {
    type: 'lighthouse',
    category: 'houses',
    label: 'Lighthouse',
    description: 'A tower with a bright light at the top',
    icon: '🗼'
  },
  pension: {
    type: 'pension',
    category: 'houses',
    label: 'Pension',
    description: 'A small hotel or boarding house',
    icon: '🏠'
  },
  shepherds_hut: {
    type: 'shepherds_hut',
    category: 'houses',
    label: "Shepherd's Hut",
    description: 'A small hut traditionally used by shepherds',
    icon: '🛖'
  },
  tiny_house: {
    type: 'tiny_house',
    category: 'houses',
    label: 'Tiny House',
    description: 'A very small house, typically under 500 sq ft',
    icon: '🏠'
  },
  tower: {
    type: 'tower',
    category: 'houses',
    label: 'Tower',
    description: 'A tall, narrow structure',
    icon: '🗼'
  },
  trullo: {
    type: 'trullo',
    category: 'houses',
    label: 'Trullo',
    description: 'Traditional stone hut with conical roof',
    icon: '🏠'
  },
  windmill: {
    type: 'windmill',
    category: 'houses',
    label: 'Windmill',
    description: 'A building with sails or vanes',
    icon: '🏠'
  },

  // Apartments
  apartment: {
    type: 'apartment',
    category: 'apartments',
    label: 'Apartment',
    description: 'A self-contained housing unit',
    icon: '🏢'
  },
  condo: {
    type: 'condo',
    category: 'apartments',
    label: 'Condominium',
    description: 'A privately owned unit in a building',
    icon: '🏢'
  },
  loft: {
    type: 'loft',
    category: 'apartments',
    label: 'Loft',
    description: 'A large, open space converted for living',
    icon: '🏢'
  },
  serviced_apartment: {
    type: 'serviced_apartment',
    category: 'apartments',
    label: 'Serviced Apartment',
    description: 'Furnished apartment with hotel-like services',
    icon: '🏢'
  },
  casa_particular: {
    type: 'casa_particular',
    category: 'apartments',
    label: 'Casa Particular',
    description: 'Private accommodation in Cuba',
    icon: '🏢'
  },

  // Secondary Units
  guesthouse: {
    type: 'guesthouse',
    category: 'secondary_units',
    label: 'Guesthouse',
    description: 'A small house for guests',
    icon: '🏠'
  },
  guest_suite: {
    type: 'guest_suite',
    category: 'secondary_units',
    label: 'Guest Suite',
    description: 'A private area within a home',
    icon: '🏠'
  },
  barn: {
    type: 'barn',
    category: 'secondary_units',
    label: 'Barn',
    description: 'A converted farm building',
    icon: '🏚️'
  },
  boat: {
    type: 'boat',
    category: 'secondary_units',
    label: 'Boat',
    description: 'A watercraft used as accommodation',
    icon: '⛵'
  },
  bus: {
    type: 'bus',
    category: 'secondary_units',
    label: 'Bus',
    description: 'A converted bus for accommodation',
    icon: '🚌'
  },
  camper_rv: {
    type: 'camper_rv',
    category: 'secondary_units',
    label: 'Camper/RV',
    description: 'A recreational vehicle',
    icon: '🚐'
  },
  campsite: {
    type: 'campsite',
    category: 'secondary_units',
    label: 'Campsite',
    description: 'A place for camping',
    icon: '⛺'
  },
  castle: {
    type: 'castle',
    category: 'secondary_units',
    label: 'Castle',
    description: 'A large fortified building',
    icon: '🏰'
  },
  cave: {
    type: 'cave',
    category: 'secondary_units',
    label: 'Cave',
    description: 'A natural underground chamber',
    icon: '🕳️'
  },
  container: {
    type: 'container',
    category: 'secondary_units',
    label: 'Container',
    description: 'A converted shipping container',
    icon: '📦'
  },
  tent: {
    type: 'tent',
    category: 'secondary_units',
    label: 'Tent',
    description: 'A portable shelter',
    icon: '⛺'
  },
  treehouse: {
    type: 'treehouse',
    category: 'secondary_units',
    label: 'Treehouse',
    description: 'A structure built in a tree',
    icon: '🌳'
  },
  yurt: {
    type: 'yurt',
    category: 'secondary_units',
    label: 'Yurt',
    description: 'A portable round tent',
    icon: '⛺'
  },

  // Shared Spaces
  hostel: {
    type: 'hostel',
    category: 'shared_spaces',
    label: 'Hostel',
    description: 'Budget accommodation with shared facilities',
    icon: '🏨'
  },
  hotel: {
    type: 'hotel',
    category: 'shared_spaces',
    label: 'Hotel',
    description: 'Commercial accommodation',
    icon: '🏨'
  },
  resort: {
    type: 'resort',
    category: 'shared_spaces',
    label: 'Resort',
    description: 'A vacation destination with amenities',
    icon: '🏖️'
  },
  bnb: {
    type: 'bnb',
    category: 'shared_spaces',
    label: 'Bed & Breakfast',
    description: 'Small lodging with breakfast',
    icon: '🏠'
  },
  boutique_hotel: {
    type: 'boutique_hotel',
    category: 'shared_spaces',
    label: 'Boutique Hotel',
    description: 'Small stylish hotel',
    icon: '🏨'
  },
  aparthotel: {
    type: 'aparthotel',
    category: 'shared_spaces',
    label: 'Aparthotel',
    description: 'Hotel with apartment-style accommodation',
    icon: '🏨'
  },
  heritage_hotel: {
    type: 'heritage_hotel',
    category: 'shared_spaces',
    label: 'Heritage Hotel',
    description: 'Hotel in a historic building',
    icon: '🏛️'
  },
  lodge: {
    type: 'lodge',
    category: 'shared_spaces',
    label: 'Lodge',
    description: 'A small hotel in the country',
    icon: '🏠'
  },
  nature_lodge: {
    type: 'nature_lodge',
    category: 'shared_spaces',
    label: 'Nature Lodge',
    description: 'Accommodation in natural settings',
    icon: '🌲'
  },
  ryokan: {
    type: 'ryokan',
    category: 'shared_spaces',
    label: 'Ryokan',
    description: 'Traditional Japanese inn',
    icon: '🏯'
  },
  riad: {
    type: 'riad',
    category: 'shared_spaces',
    label: 'Riad',
    description: 'Traditional Moroccan house',
    icon: '🏛️'
  },

  // Unique Stays
  airplane: {
    type: 'airplane',
    category: 'unique_stays',
    label: 'Airplane',
    description: 'A converted aircraft',
    icon: '✈️'
  },
  train: {
    type: 'train',
    category: 'unique_stays',
    label: 'Train',
    description: 'A converted train car',
    icon: '🚂'
  },
  island: {
    type: 'island',
    category: 'unique_stays',
    label: 'Private Island',
    description: 'An entire island for exclusive use',
    icon: '🏝️'
  },
  religious_building: {
    type: 'religious_building',
    category: 'unique_stays',
    label: 'Religious Building',
    description: 'Converted religious structure',
    icon: '⛪'
  },
  ski_in_ski_out: {
    type: 'ski_in_ski_out',
    category: 'unique_stays',
    label: 'Ski-in/Ski-out',
    description: 'Direct access to ski slopes',
    icon: '🎿'
  },
  beach_hut: {
    type: 'beach_hut',
    category: 'unique_stays',
    label: 'Beach Hut',
    description: 'Small structure on the beach',
    icon: '🏖️'
  },
  igloo: {
    type: 'igloo',
    category: 'unique_stays',
    label: 'Igloo',
    description: 'Dome-shaped shelter made of ice',
    icon: '🏔️'
  },
  monastery: {
    type: 'monastery',
    category: 'unique_stays',
    label: 'Monastery',
    description: 'Religious community building',
    icon: '⛪'
  },
  palace: {
    type: 'palace',
    category: 'unique_stays',
    label: 'Palace',
    description: 'Grand residence of royalty',
    icon: '🏰'
  },
  parking_space: {
    type: 'parking_space',
    category: 'unique_stays',
    label: 'Parking Space',
    description: 'Space for parking vehicles',
    icon: '🅿️'
  },
  plane: {
    type: 'plane',
    category: 'unique_stays',
    label: 'Plane',
    description: 'Aircraft converted for accommodation',
    icon: '✈️'
  },
  prison: {
    type: 'prison',
    category: 'unique_stays',
    label: 'Prison',
    description: 'Converted former prison',
    icon: '🏢'
  },
  cave_house: {
    type: 'cave_house',
    category: 'unique_stays',
    label: 'Cave House',
    description: 'House built into a cave',
    icon: '🕳️'
  },
  earth_home: {
    type: 'earth_home',
    category: 'unique_stays',
    label: 'Earth Home',
    description: 'Home built into the earth',
    icon: '🏠'
  },
  floating_house: {
    type: 'floating_house',
    category: 'unique_stays',
    label: 'Floating House',
    description: 'House that floats on water',
    icon: '🏠'
  },
  ice_hotel: {
    type: 'ice_hotel',
    category: 'unique_stays',
    label: 'Ice Hotel',
    description: 'Hotel made entirely of ice',
    icon: '🏔️'
  },
  mud_house: {
    type: 'mud_house',
    category: 'unique_stays',
    label: 'Mud House',
    description: 'House built with mud construction',
    icon: '🏠'
  },
  rock_house: {
    type: 'rock_house',
    category: 'unique_stays',
    label: 'Rock House',
    description: 'House built into or from rock',
    icon: '🏠'
  },
  solar_house: {
    type: 'solar_house',
    category: 'unique_stays',
    label: 'Solar House',
    description: 'Eco-friendly solar-powered house',
    icon: '☀️'
  },
  sustainable_house: {
    type: 'sustainable_house',
    category: 'unique_stays',
    label: 'Sustainable House',
    description: 'Environmentally sustainable accommodation',
    icon: '🌱'
  },
  underground_house: {
    type: 'underground_house',
    category: 'unique_stays',
    label: 'Underground House',
    description: 'House built underground',
    icon: '🏠'
  }
};

export const PROPERTY_CATEGORIES: Record<PropertyCategory, { label: string; description: string; icon: string }> = {
  houses: {
    label: 'Houses',
    description: 'Entire houses and residential buildings',
    icon: '🏠'
  },
  apartments: {
    label: 'Apartments',
    description: 'Apartments, condos, and urban units',
    icon: '🏢'
  },
  secondary_units: {
    label: 'Secondary Units',
    description: 'Guest houses, converted spaces, and unique structures',
    icon: '🏘️'
  },
  shared_spaces: {
    label: 'Shared Spaces',
    description: 'Hotels, hostels, and commercial accommodations',
    icon: '🏨'
  },
  unique_stays: {
    label: 'Unique Stays',
    description: 'One-of-a-kind and unconventional accommodations',
    icon: '✨'
  }
};

export const getPropertyTypesByCategory = (category: PropertyCategory): PropertyTypeInfo[] => {
  return Object.values(PROPERTY_TYPES).filter(type => type.category === category);
};

export const getPropertyTypeInfo = (type: PropertyType): PropertyTypeInfo => {
  return PROPERTY_TYPES[type];
};
