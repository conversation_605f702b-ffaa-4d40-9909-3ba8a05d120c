// Airbnb Property Types - Complete list based on Airbnb's categories

export type PropertyType =
  // Houses
  | 'house'
  | 'cabin'
  | 'villa'
  | 'cottage'
  | 'bungalow'
  | 'chalet'
  | 'cycladic_house'
  | 'dammuso'
  | 'dome_house'
  | 'earth_house'
  | 'farm_stay'
  | 'houseboat'
  | 'hut'
  | 'lighthouse'
  | 'pension'
  | 'shepherds_hut'
  | 'tiny_house'
  | 'tower'
  | 'trullo'
  | 'windmill'

  // Apartments
  | 'apartment'
  | 'condo'
  | 'loft'
  | 'serviced_apartment'
  | 'casa_particular'

  // Secondary Units
  | 'guesthouse'
  | 'guest_suite'
  | 'barn'
  | 'boat'
  | 'bus'
  | 'camper_rv'
  | 'campsite'
  | 'castle'
  | 'cave'
  | 'container'
  | 'tent'
  | 'treehouse'
  | 'yurt'

  // Shared Spaces
  | 'hostel'
  | 'hotel'
  | 'resort'
  | 'bnb'
  | 'boutique_hotel'
  | 'aparthotel'
  | 'heritage_hotel'
  | 'lodge'
  | 'nature_lodge'
  | 'ryokan'
  | 'riad'

  // Unique Stays
  | 'airplane'
  | 'train'
  | 'island'
  | 'religious_building'
  | 'ski_in_ski_out'
  | 'beach_hut'
  | 'igloo'
  | 'monastery'
  | 'palace'
  | 'parking_space'
  | 'plane'
  | 'prison'
  | 'cave_house'
  | 'earth_home'
  | 'floating_house'
  | 'ice_hotel'
  | 'mud_house'
  | 'rock_house'
  | 'solar_house'
  | 'sustainable_house'
  | 'underground_house';

export type PropertyCategory =
  | 'houses'
  | 'apartments'
  | 'secondary_units'
  | 'shared_spaces'
  | 'unique_stays';

export interface PropertyTypeInfo {
  type: PropertyType;
  category: PropertyCategory;
  label: string;
  description: string;
  icon: string;
}

export type AreaUnit = 'sqft' | 'sqm';

export interface Property {
  id: string;
  organizationId: string;
  name: string;
  type: PropertyType;
  category: PropertyCategory;
  description?: string;

  // Location
  address: string;
  city: string;
  state?: string;
  country: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;

  // Property Details
  bedrooms: number;
  bathrooms: number;
  maxGuests: number;
  area?: number;
  areaUnit?: AreaUnit;

  // Amenities
  amenities: string[];

  // Images
  images: PropertyImage[];

  // Status
  isActive: boolean;
  isListed: boolean;

  // Metadata
  createdAt: Date;
  updatedAt: Date;

  // Relations
  organization?: {
    id: string;
    name: string;
  };
}

export interface PropertyImage {
  id: string;
  url: string;
  key: string;
  caption?: string;
  isMain: boolean;
  order: number;
}

export interface CreatePropertyData {
  name: string;
  type: PropertyType;
  description?: string;
  address: string;
  city: string;
  state?: string;
  country: string;
  postalCode?: string;
  bedrooms: number;
  bathrooms: number;
  maxGuests: number;
  area?: number;
  areaUnit?: AreaUnit;
  amenities: string[];
  images?: File[];
}

export interface UpdatePropertyData extends Partial<CreatePropertyData> {
  isActive?: boolean;
  isListed?: boolean;
}

// Common amenities list
export const COMMON_AMENITIES = [
  // Basic
  'wifi',
  'kitchen',
  'washer',
  'dryer',
  'air_conditioning',
  'heating',
  'dedicated_workspace',
  'tv',
  'hair_dryer',
  'iron',

  // Bathroom
  'shampoo',
  'body_soap',
  'hot_water',
  'shower_gel',
  'conditioner',

  // Bedroom & Laundry
  'essentials',
  'hangers',
  'bed_linens',
  'extra_pillows_blankets',
  'room_darkening_shades',
  'clothing_storage',

  // Entertainment
  'hdtv',
  'netflix',
  'amazon_prime',
  'disney_plus',
  'hbo_max',
  'sound_system',
  'exercise_equipment',
  'piano',
  'pool_table',
  'ping_pong_table',

  // Family
  'baby_bath',
  'baby_monitor',
  'babysitter_recommendations',
  'bathtub',
  'changing_table',
  'childrens_books_toys',
  'childrens_dinnerware',
  'crib',
  'fireplace_guards',
  'high_chair',
  'outlet_covers',
  'pack_n_play_travel_crib',
  'stair_gates',
  'table_corner_guards',
  'window_guards',

  // Safety
  'smoke_alarm',
  'carbon_monoxide_alarm',
  'fire_extinguisher',
  'first_aid_kit',
  'security_cameras_exterior',
  'lockbox',

  // Accessibility
  'step_free_access',
  'wide_hallway_clearance',
  'wide_doorway',
  'accessible_height_bed',
  'accessible_height_toilet',
  'step_free_shower',
  'shower_chair',
  'accessible_parking_spot',

  // Kitchen & Dining
  'refrigerator',
  'microwave',
  'cooking_basics',
  'dishes_silverware',
  'freezer',
  'oven',
  'stove',
  'coffee_maker',
  'dishwasher',
  'toaster',
  'blender',
  'wine_glasses',

  // Location Features
  'private_entrance',
  'paid_parking_premises',
  'free_parking_premises',
  'gym',
  'pool',
  'hot_tub',
  'patio_balcony',
  'bbq_grill',
  'fire_pit',
  'pool_table',
  'outdoor_furniture',
  'beach_access',
  'ski_in_ski_out',
  'waterfront',
  'beachfront',
  'lake_access',

  // Services
  'breakfast',
  'cleaning_during_stay',
  'self_check_in',
  'keypad',
  'pets_allowed',
  'smoking_allowed',
  'suitable_for_events',
  'long_term_stays_allowed'
] as const;

export type Amenity = typeof COMMON_AMENITIES[number];
