"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Star } from "lucide-react";
import { CreateReviewData } from "../types";
import { ImageUpload } from "@/components/ui/image-upload";

interface AddReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreateReviewData) => Promise<void>;
  guestName: string;
}

const COMMON_TAGS = [
  'clean',
  'quiet',
  'respectful',
  'communicative',
  'punctual',
  'followed rules',
  'left property tidy',
  'friendly',
  'problematic',
  'noisy',
  'messy',
  'unresponsive',
  'late',
  'broke rules',
  'damaged property'
];

export function AddReviewModal({ isOpen, onClose, onSubmit, guestName }: AddReviewModalProps) {
  const [formData, setFormData] = useState<CreateReviewData>({
    rating: 0,
    title: "",
    comment: "",
    stayDates: undefined,
    propertyType: undefined,
    wouldRecommend: undefined,
    tags: [],
    images: [],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (formData.rating === 0) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      setFormData({
        rating: 0,
        title: "",
        comment: "",
        stayDates: undefined,
        propertyType: undefined,
        wouldRecommend: undefined,
        tags: [],
        images: [],
      });
      onClose();
    } catch (error) {
      console.error('Error submitting review:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setFormData({
        rating: 0,
        title: "",
        comment: "",
        stayDates: undefined,
        propertyType: undefined,
        wouldRecommend: undefined,
        tags: [],
        images: [],
      });
      onClose();
    }
  };

  const handleStarClick = (rating: number) => {
    setFormData({ ...formData, rating });
  };

  const handleTagToggle = (tag: string) => {
    const currentTags = formData.tags || [];
    const newTags = currentTags.includes(tag)
      ? currentTags.filter(t => t !== tag)
      : [...currentTags, tag];
    setFormData({ ...formData, tags: newTags });
  };

  const handleImagesChange = (imageFiles: any[]) => {
    const files = imageFiles.map(img => img.file);
    setFormData({ ...formData, images: files });
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-airbnb-hof">Add Review for {guestName}</DialogTitle>
          <DialogDescription>
            Share your experience to help other hosts make informed decisions.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Rating */}
          <div className="space-y-2">
            <Label>Overall Rating *</Label>
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => handleStarClick(star)}
                  className="p-1 hover:scale-110 transition-transform"
                >
                  <Star
                    className={`h-8 w-8 ${
                      star <= formData.rating
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
              <span className="ml-2 text-sm text-airbnb-foggy">
                {formData.rating > 0 && `${formData.rating} star${formData.rating !== 1 ? 's' : ''}`}
              </span>
            </div>
          </div>

          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Review Title</Label>
            <Input
              id="title"
              placeholder="Brief summary of your experience"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            />
          </div>

          {/* Comment */}
          <div className="space-y-2">
            <Label htmlFor="comment">Review Details</Label>
            <Textarea
              id="comment"
              placeholder="Describe your experience with this guest..."
              value={formData.comment}
              onChange={(e) => setFormData({ ...formData, comment: e.target.value })}
              rows={4}
            />
          </div>

          {/* Stay Dates */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="checkIn">Check-in Date</Label>
              <Input
                id="checkIn"
                type="date"
                value={formData.stayDates?.checkIn || ''}
                onChange={(e) => setFormData({
                  ...formData,
                  stayDates: {
                    ...formData.stayDates,
                    checkIn: e.target.value,
                    checkOut: formData.stayDates?.checkOut || ''
                  }
                })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="checkOut">Check-out Date</Label>
              <Input
                id="checkOut"
                type="date"
                value={formData.stayDates?.checkOut || ''}
                onChange={(e) => setFormData({
                  ...formData,
                  stayDates: {
                    ...formData.stayDates,
                    checkIn: formData.stayDates?.checkIn || '',
                    checkOut: e.target.value
                  }
                })}
              />
            </div>
          </div>

          {/* Property Type */}
          <div className="space-y-2">
            <Label>Property Type</Label>
            <Select
              value={formData.propertyType || ''}
              onValueChange={(value: any) => setFormData({ ...formData, propertyType: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select property type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="apartment">Apartment</SelectItem>
                <SelectItem value="house">House</SelectItem>
                <SelectItem value="room">Room</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Would Recommend */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="recommend"
              checked={formData.wouldRecommend || false}
              onCheckedChange={(checked) => setFormData({ ...formData, wouldRecommend: !!checked })}
            />
            <Label htmlFor="recommend">I would recommend this guest to other hosts</Label>
          </div>

          {/* Images */}
          <div className="space-y-2">
            <Label>Photos (optional)</Label>
            <p className="text-sm text-airbnb-foggy">
              Add up to 10 photos to illustrate your experience (max 5MB each)
            </p>
            <ImageUpload
              maxFiles={10}
              maxSizePerFile={5}
              onImagesChange={handleImagesChange}
              variant="review"
            />
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <Label>Tags (select all that apply)</Label>
            <div className="grid grid-cols-3 gap-2">
              {COMMON_TAGS.map((tag) => (
                <div key={tag} className="flex items-center space-x-2">
                  <Checkbox
                    id={tag}
                    checked={formData.tags?.includes(tag) || false}
                    onCheckedChange={() => handleTagToggle(tag)}
                  />
                  <Label htmlFor={tag} className="text-sm">{tag}</Label>
                </div>
              ))}
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-airbnb-rausch hover:bg-airbnb-rausch/90"
              disabled={isSubmitting || formData.rating === 0}
            >
              {isSubmitting ? "Adding..." : "Add Review"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
