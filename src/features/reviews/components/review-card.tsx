"use client";

import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, Calendar, Home, ThumbsUp, ThumbsDown } from "lucide-react";
import { Review } from "../types";

interface ReviewCardProps {
  review: Review;
}

export function ReviewCard({ review }: ReviewCardProps) {
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  const getPropertyTypeIcon = (type?: string) => {
    switch (type) {
      case 'apartment': return '🏢';
      case 'house': return '🏠';
      case 'room': return '🚪';
      default: return '🏠';
    }
  };

  const formatStayDates = (stayDates?: { checkIn: string; checkOut: string }) => {
    if (!stayDates?.checkIn || !stayDates?.checkOut) return null;

    const checkIn = new Date(stayDates.checkIn).toLocaleDateString();
    const checkOut = new Date(stayDates.checkOut).toLocaleDateString();
    return `${checkIn} - ${checkOut}`;
  };

  const getPositiveTags = (tags?: string[]) => {
    if (!tags) return [];
    const positive = ['clean', 'quiet', 'respectful', 'communicative', 'punctual', 'followed rules', 'left property tidy', 'friendly'];
    return tags.filter(tag => positive.includes(tag));
  };

  const getNegativeTags = (tags?: string[]) => {
    if (!tags) return [];
    const negative = ['problematic', 'noisy', 'messy', 'unresponsive', 'late', 'broke rules', 'damaged property'];
    return tags.filter(tag => negative.includes(tag));
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={review.reviewer?.imageUrl} />
              <AvatarFallback>
                {review.reviewer?.firstName?.[0]}{review.reviewer?.lastName?.[0]}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center space-x-2">
                <h4 className="font-semibold text-airbnb-hof">
                  {review.title || 'Guest Review'}
                </h4>
                <div className="flex items-center">
                  {renderStars(review.rating)}
                </div>
              </div>
              <div className="flex items-center space-x-2 text-sm text-airbnb-foggy">
                <span>{review.reviewer?.firstName} {review.reviewer?.lastName}</span>
                <span>•</span>
                <Calendar className="h-3 w-3" />
                <span>{new Date(review.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {review.wouldRecommend !== undefined && (
              <Badge
                className={review.wouldRecommend
                  ? "bg-green-100 text-green-800 border-green-200"
                  : "bg-red-100 text-red-800 border-red-200"
                }
              >
                {review.wouldRecommend ? (
                  <>
                    <ThumbsUp className="h-3 w-3 mr-1" />
                    Recommended
                  </>
                ) : (
                  <>
                    <ThumbsDown className="h-3 w-3 mr-1" />
                    Not Recommended
                  </>
                )}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {review.comment && (
          <p className="text-airbnb-foggy">{review.comment}</p>
        )}

        {/* Review Images */}
        {review.images && review.images.length > 0 && (
          <div className="space-y-2">
            <h5 className="text-sm font-medium text-airbnb-hof">Photos</h5>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {review.images.map((image, index) => (
                <div key={index} className="relative group">
                  <img
                    src={image.url}
                    alt={image.caption || `Review photo ${index + 1}`}
                    className="w-full aspect-video object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                    onClick={() => {
                      // TODO: Open image in modal/lightbox
                      window.open(image.url, '_blank');
                    }}
                  />
                  {image.caption && (
                    <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white text-xs p-2 rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity">
                      {image.caption}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Stay Details */}
        <div className="flex flex-wrap items-center gap-4 text-sm text-airbnb-foggy">
          {review.propertyType && (
            <div className="flex items-center space-x-1">
              <span>{getPropertyTypeIcon(review.propertyType)}</span>
              <span className="capitalize">{review.propertyType}</span>
            </div>
          )}

          {formatStayDates(review.stayDates) && (
            <div className="flex items-center space-x-1">
              <Calendar className="h-4 w-4" />
              <span>{formatStayDates(review.stayDates)}</span>
            </div>
          )}
        </div>

        {/* Tags */}
        {review.tags && review.tags.length > 0 && (
          <div className="space-y-2">
            {getPositiveTags(review.tags).length > 0 && (
              <div className="flex flex-wrap gap-2">
                {getPositiveTags(review.tags).map((tag) => (
                  <Badge
                    key={tag}
                    className="bg-green-100 text-green-800 border-green-200 text-xs"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            )}

            {getNegativeTags(review.tags).length > 0 && (
              <div className="flex flex-wrap gap-2">
                {getNegativeTags(review.tags).map((tag) => (
                  <Badge
                    key={tag}
                    className="bg-red-100 text-red-800 border-red-200 text-xs"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
