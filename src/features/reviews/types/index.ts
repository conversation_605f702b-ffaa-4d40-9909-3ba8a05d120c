export interface ReviewImage {
  url: string;
  key: string;
  caption?: string;
}

export interface Review {
  id: string;
  guestId: string;
  organizationId: string;
  reviewerId: string;
  rating: number; // 1-5
  title?: string;
  comment?: string;
  stayDates?: {
    checkIn: string;
    checkOut: string;
  };
  propertyType?: 'apartment' | 'house' | 'room';
  wouldRecommend?: boolean;
  tags?: string[];
  images?: ReviewImage[];
  createdAt: Date;
  updatedAt: Date;
  // Relations
  reviewer?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
  };
}

export interface CreateReviewData {
  rating: number;
  title?: string;
  comment?: string;
  stayDates?: {
    checkIn: string;
    checkOut: string;
  };
  propertyType?: 'apartment' | 'house' | 'room';
  wouldRecommend?: boolean;
  tags?: string[];
  images?: File[];
}

export interface ReviewStats {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}
