"use client";

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { Organization } from '@/features/organizations/types';

export function useOrganizations() {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isLoaded, user } = useUser();

  const fetchOrganizations = async () => {
    if (!isLoaded || !user) return;

    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/organizations');
      
      if (!response.ok) {
        throw new Error('Failed to fetch organizations');
      }
      
      const data = await response.json();
      setOrganizations(data);
    } catch (err) {
      console.error('Error fetching organizations:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch organizations');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrganizations();
  }, [isLoaded, user]);

  const refetch = () => {
    fetchOrganizations();
  };

  return {
    organizations,
    isLoading,
    error,
    refetch,
  };
}

export function useOrganization(organizationId: string) {
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isLoaded, user } = useUser();

  const fetchOrganization = async () => {
    if (!isLoaded || !user || !organizationId) return;

    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch(`/api/organizations/${organizationId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Organization not found');
        }
        if (response.status === 403) {
          throw new Error('Access denied');
        }
        throw new Error('Failed to fetch organization');
      }
      
      const data = await response.json();
      setOrganization(data);
    } catch (err) {
      console.error('Error fetching organization:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch organization');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchOrganization();
  }, [isLoaded, user, organizationId]);

  const refetch = () => {
    fetchOrganization();
  };

  return {
    organization,
    isLoading,
    error,
    refetch,
  };
}
