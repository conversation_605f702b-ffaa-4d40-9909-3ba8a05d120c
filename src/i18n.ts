import { getRequestConfig } from 'next-intl/server';

// Can be imported from a shared config
const locales = ['en', 'br', 'es'];

export default getRequestConfig(async ({ requestLocale }) => {
  // This function gets called on each request
  let locale = await requestLocale;

  // Ensure that a valid locale is used
  if (!locale || !locales.includes(locale)) {
    locale = 'en';
  }

  return {
    locale,
    messages: (await import(`./languages/${locale}.json`)).default
  };
});
