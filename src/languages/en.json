{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "search": "Search", "filter": "Filter", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "continue": "Continue", "close": "Close", "yes": "Yes", "no": "No", "required": "Required", "optional": "Optional"}, "navigation": {"dashboard": "Dashboard", "properties": "Properties", "guests": "Guests", "reviews": "Reviews", "organizations": "Organizations", "account": "Account", "signIn": "Sign In", "signUp": "Sign Up", "signOut": "Sign Out"}, "landing": {"title": "The Ultimate Airbnb Guest Directory & Management Tool", "subtitle": "Build trust in your rental community. Share guest reviews, check reputation scores, and make informed decisions before accepting bookings.", "getStarted": "Get Started", "learnMore": "Learn More", "features": {"title": "Everything You Need to Manage Guests", "subtitle": "Comprehensive tools designed specifically for Airbnb hosts and property managers.", "guestDirectory": {"title": "Guest Directory", "description": "Comprehensive database of guest profiles with contact information, preferences, and history."}, "reviewSystem": {"title": "Review System", "description": "Rate guests from 1-5 stars and share detailed reviews to help other hosts make informed decisions."}, "organizationManagement": {"title": "Organization Management", "description": "Manage multiple properties and team members with role-based access and permissions."}, "reputationScores": {"title": "Reputation Scores", "description": "Automatic calculation of guest reputation based on reviews from the entire community."}, "quickSearch": {"title": "Quick Search", "description": "Instantly search and verify guest information before accepting bookings."}, "globalNetwork": {"title": "Global Network", "description": "Connect with hosts worldwide and access a growing database of guest reviews."}}, "pricing": {"title": "Simple, Transparent Pricing", "subtitle": "Pay per user seat. Scale as your organization grows.", "plan": {"title": "Professional", "price": "$9", "period": "/user/month", "description": "Perfect for property managers and host teams", "features": ["Unlimited guest profiles", "Unlimited reviews", "Organization management", "Advanced search & filters", "Priority support"]}, "startTrial": "Start Free Trial"}, "cta": {"title": "Ready to Build Trust in Your Rental Community?", "subtitle": "Join thousands of hosts who are already using GuestFile to make better booking decisions.", "getStartedToday": "Get Started Today"}}, "dashboard": {"title": "Dashboard", "subtitle": "Welcome to your guest management dashboard", "stats": {"totalGuests": "Total Guests", "totalReviews": "Total Reviews", "organizations": "Organizations", "avgRating": "Avg. <PERSON>ing", "activeOrganizations": "Active organizations", "overallGuestRating": "Overall guest rating"}, "quickActions": {"title": "Quick Actions", "subtitle": "Get started with common tasks", "createFirstOrganization": "Create your first organization to get started"}, "recentActivity": {"title": "Recent Activity", "subtitle": "Latest updates and reviews", "noActivity": "No recent activity"}}, "organizations": {"title": "Organizations", "subtitle": "Manage your property organizations and teams", "create": "Create Organization", "createFirst": "Create Your First Organization", "noOrganizations": {"title": "No organizations yet", "description": "Create your first organization to start managing guests and reviews."}, "form": {"title": "Create Organization", "subtitle": "Set up your property management organization to start managing guest reviews.", "name": "Organization Name", "namePlaceholder": "e.g., Sunset Apartments", "description": "Description", "descriptionPlaceholder": "Brief description of your property or organization", "address": "Address", "addressPlaceholder": "Street address", "city": "City", "cityPlaceholder": "City", "state": "State/Province", "statePlaceholder": "State or Province", "country": "Country", "countryPlaceholder": "Country", "zipCode": "ZIP/Postal Code", "zipCodePlaceholder": "ZIP or Postal Code", "phone": "Phone", "phonePlaceholder": "Phone number", "email": "Email", "emailPlaceholder": "<EMAIL>", "website": "Website", "websitePlaceholder": "https://www.organization.com", "creating": "Creating...", "createButton": "Create Organization"}, "manage": "Manage Organization", "maxUsers": "Max {count} users"}, "guests": {"title": "Guest Directory", "subtitle": "Manage and search through your guest database", "add": "Add Guest", "addFirst": "Add Your First Guest", "searchPlaceholder": "Search guests by name or email...", "noGuests": {"title": "No guests yet", "description": "Start building your guest directory by adding your first guest."}, "noGuestsFound": {"title": "No guests found", "description": "Try adjusting your search terms or add a new guest."}, "form": {"title": "Add <PERSON> Guest", "subtitle": "Create a new guest profile for your directory.", "firstName": "First Name", "firstNamePlaceholder": "<PERSON>", "lastName": "Last Name", "lastNamePlaceholder": "<PERSON><PERSON>", "email": "Email", "emailPlaceholder": "<EMAIL>", "phone": "Phone", "phonePlaceholder": "+****************", "dateOfBirth": "Date of Birth", "nationality": "Nationality", "nationalityPlaceholder": "American", "documentType": "Document Type", "documentTypePlaceholder": "Select document type", "documentTypes": {"passport": "Passport", "id": "ID Card", "driverLicense": "Driver's License"}, "documentNumber": "Document Number", "documentNumberPlaceholder": "Document number", "emergencyContact": "Emergency Contact", "emergencyContactPlaceholder": "Contact name", "emergencyPhone": "Emergency Phone", "emergencyPhonePlaceholder": "Emergency phone number", "notes": "Notes", "notesPlaceholder": "Additional notes about the guest", "creating": "Creating...", "createButton": "Create Guest"}, "badges": {"reviewed": "Reviewed", "new": "New"}, "viewProfile": "View Profile", "reviews": "{count} reviews"}, "auth": {"signIn": {"title": "Sign in to your account", "subtitle": "Welcome back! Please enter your details."}, "signUp": {"title": "Create your account", "subtitle": "Welcome! Please fill in the details to get started."}}, "messages": {"success": {"organizationCreated": "Organization created successfully.", "guestCreated": "Guest created successfully."}, "error": {"organizationCreateFailed": "Failed to create organization. Please try again.", "guestCreateFailed": "Failed to create guest. Please try again.", "generic": "Something went wrong. Please try again."}}}