import { db } from '@/db';
import { users } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { currentUser } from '@clerk/nextjs/server';

/**
 * Gets or creates a user in the database based on Clerk authentication
 * This handles the case where a user exists in Clerk but not in our database
 */
export async function getOrCreateUser(userId: string) {
  // Try to find user in database
  let user = await db.query.users.findFirst({
    where: eq(users.clerkId, userId),
  });

  if (!user) {
    // User exists in Clerk but not in our database, create them
    const clerkUser = await currentUser();
    
    if (!clerkUser) {
      throw new Error('User not found in Clerk');
    }

    const [newUser] = await db.insert(users).values({
      clerkId: userId,
      email: clerkUser.emailAddresses[0]?.emailAddress || '',
      firstName: clerkUser.firstName || '',
      lastName: clerkUser.lastName || '',
      imageUrl: clerkUser.imageUrl || '',
    }).returning();

    user = newUser;
    console.log('Created user in database:', userId);
  }

  return user;
}
