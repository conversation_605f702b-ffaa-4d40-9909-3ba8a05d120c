import { create<PERSON>lerkClient } from '@clerk/backend';
import { CreateOrganizationData } from '@/features/organizations/types';

// Initialize Clerk client
const clerkClient = createClerkClient({
  secretKey: process.env.CLERK_SECRET_KEY,
});

/**
 * Creates an organization in Clerk
 */
export async function createClerkOrganization(
  data: CreateOrganizationData,
  createdBy: string
): Promise<{ id: string; slug: string | null }> {
  try {
    // Generate a slug from the organization name
    const slug = generateSlugFromName(data.name);

    const clerkOrgData = {
      name: data.name,
      slug,
      created_by: createdBy,
      public_metadata: {
        description: data.description || null,
        website: data.website || null,
      },
      private_metadata: {
        address: data.address || null,
        city: data.city || null,
        state: data.state || null,
        country: data.country || null,
        zipCode: data.zipCode || null,
        phone: data.phone || null,
        email: data.email || null,
      },
    };

    const organization = await clerkClient.organizations.createOrganization(clerkOrgData);

    return {
      id: organization.id,
      slug: organization.slug,
    };
  } catch (error) {
    console.error('Error creating Clerk organization:', error);
    throw new Error('Failed to create organization in Clerk');
  }
}

/**
 * Updates an organization in Clerk
 */
export async function updateClerkOrganization(
  clerkOrgId: string,
  data: Partial<CreateOrganizationData>
): Promise<void> {
  try {
    const updateData: any = {};

    if (data.name) {
      updateData.name = data.name;
      updateData.slug = generateSlugFromName(data.name);
    }

    if (data.description !== undefined || data.website !== undefined) {
      updateData.public_metadata = {};
      if (data.description !== undefined) updateData.public_metadata.description = data.description;
      if (data.website !== undefined) updateData.public_metadata.website = data.website;
    }

    const hasPrivateMetadata = [
      'address', 'city', 'state', 'country', 'zipCode', 'phone', 'email'
    ].some(key => data[key as keyof CreateOrganizationData] !== undefined);

    if (hasPrivateMetadata) {
      updateData.private_metadata = {};
      if (data.address !== undefined) updateData.private_metadata.address = data.address;
      if (data.city !== undefined) updateData.private_metadata.city = data.city;
      if (data.state !== undefined) updateData.private_metadata.state = data.state;
      if (data.country !== undefined) updateData.private_metadata.country = data.country;
      if (data.zipCode !== undefined) updateData.private_metadata.zipCode = data.zipCode;
      if (data.phone !== undefined) updateData.private_metadata.phone = data.phone;
      if (data.email !== undefined) updateData.private_metadata.email = data.email;
    }

    await clerkClient.organizations.updateOrganization(clerkOrgId, updateData);
  } catch (error) {
    console.error('Error updating Clerk organization:', error);
    throw new Error('Failed to update organization in Clerk');
  }
}

/**
 * Deletes an organization in Clerk
 */
export async function deleteClerkOrganization(clerkOrgId: string): Promise<void> {
  try {
    await clerkClient.organizations.deleteOrganization(clerkOrgId);
  } catch (error) {
    console.error('Error deleting Clerk organization:', error);
    throw new Error('Failed to delete organization in Clerk');
  }
}

/**
 * Gets an organization from Clerk
 */
export async function getClerkOrganization(clerkOrgId: string) {
  try {
    return await clerkClient.organizations.getOrganization({ organizationId: clerkOrgId });
  } catch (error) {
    console.error('Error fetching Clerk organization:', error);
    throw new Error('Failed to fetch organization from Clerk');
  }
}

/**
 * Generates a URL-friendly slug from organization name
 */
function generateSlugFromName(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
    .substring(0, 50); // Limit length
}
