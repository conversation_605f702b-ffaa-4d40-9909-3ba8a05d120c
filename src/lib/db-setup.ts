import { db } from '@/db';
import { users } from '@/db/schema';

export async function setupTestUser() {
  try {
    // Create a test user for development
    const testUser = await db.insert(users).values({
      clerkId: 'test-user-id',
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
    }).returning();

    console.log('Test user created:', testUser[0]);
    return testUser[0];
  } catch (error) {
    console.error('Error creating test user:', error);
    throw error;
  }
}
