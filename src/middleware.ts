import { NextResponse } from "next/server";
import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import createMiddleware from "next-intl/middleware";

const intlMiddleware = createMiddleware({
  locales: ["en", "br", "es"],
  defaultLocale: "en",
});

const isPublicRoute = createRouteMatcher([
  "/",
  "/:locale",
  "/:locale/sign-in",
  "/:locale/sign-up",
  "/:locale/sign-in/[[...sign-in]]",
  "/:locale/sign-up/[[...sign-up]]",
  "/api/webhooks/(.*)",
]);

const isProtectedRoute = createRouteMatcher([
  "/:locale/dashboard(.*)",
  "/api/organizations(.*)",
  "/api/guests(.*)",
  "/api/reviews(.*)",
]);

export default clerkMiddleware(
  async (auth, request) => {
    // 1. Always allow API routes (except protected ones)
    if (request.nextUrl.pathname.startsWith("/api/")) {
      if (isProtectedRoute(request)) {
        const { userId } = await auth();
        if (!userId) {
          return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }
      }
      return NextResponse.next();
    }

    const locale = request.nextUrl.pathname.split("/")[1] || "en";
    const { userId } = await auth();

    // 2. If not authenticated, only allow public routes
    if (!userId) {
      if (isPublicRoute(request)) {
        return intlMiddleware(request);
      }
      return NextResponse.redirect(new URL(`/${locale}/sign-in`, request.url));
    }

    // 3. User is authenticated - allow the request to proceed with internationalization
    return intlMiddleware(request);
  }
);

export const config = {
  matcher: [
    "/((?!.+\\.[\\w]+$|_next).*)",
    "/",
    "/(api|trpc)(.*)",
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
