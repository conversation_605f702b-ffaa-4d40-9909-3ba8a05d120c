"use client";

import { Button } from "@/components/ui/button";
import { Grid3X3, Table } from "lucide-react";

interface ViewToggleProps {
  view: 'cards' | 'table';
  onViewChange: (view: 'cards' | 'table') => void;
}

export function ViewToggle({ view, onViewChange }: ViewToggleProps) {
  return (
    <div className="flex items-center space-x-1 border rounded-lg p-1">
      <Button
        variant={view === 'cards' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onViewChange('cards')}
        className={`h-8 px-3 ${
          view === 'cards' 
            ? 'bg-airbnb-rausch hover:bg-airbnb-rausch/90 text-white' 
            : 'hover:bg-gray-100'
        }`}
      >
        <Grid3X3 className="h-4 w-4 mr-1" />
        Cards
      </Button>
      <Button
        variant={view === 'table' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onViewChange('table')}
        className={`h-8 px-3 ${
          view === 'table' 
            ? 'bg-airbnb-rausch hover:bg-airbnb-rausch/90 text-white' 
            : 'hover:bg-gray-100'
        }`}
      >
        <Table className="h-4 w-4 mr-1" />
        Table
      </Button>
    </div>
  );
}
