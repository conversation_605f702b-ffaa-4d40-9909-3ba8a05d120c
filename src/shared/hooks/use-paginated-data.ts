"use client";

import { useState, useEffect, useCallback } from "react";
import { PaginationParams, PaginatedResponse } from "@/shared/types/pagination";

interface UsePaginatedDataOptions {
  initialPage?: number;
  initialLimit?: number;
  initialSearch?: string;
}

interface UsePaginatedDataReturn<T> {
  data: T[];
  pagination: PaginatedResponse<T>['pagination'];
  isLoading: boolean;
  error: string | null;
  params: PaginationParams;
  updateParams: (newParams: Partial<PaginationParams>) => void;
  refetch: () => void;
}

export function usePaginatedData<T>(
  fetchFn: (params: PaginationParams) => Promise<PaginatedResponse<T>>,
  options: UsePaginatedDataOptions = {}
): UsePaginatedDataReturn<T> {
  const {
    initialPage = 1,
    initialLimit = 10,
    initialSearch = "",
  } = options;

  const [data, setData] = useState<T[]>([]);
  const [pagination, setPagination] = useState<PaginatedResponse<T>['pagination']>({
    page: initialPage,
    limit: initialLimit,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [params, setParams] = useState<PaginationParams>({
    page: initialPage,
    limit: initialLimit,
    search: initialSearch,
  });

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetchFn(params);
      setData(response.data);
      setPagination(response.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setData([]);
      setPagination({
        page: params.page,
        limit: params.limit,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      });
    } finally {
      setIsLoading(false);
    }
  }, [fetchFn, params]);

  const updateParams = useCallback((newParams: Partial<PaginationParams>) => {
    setParams(prev => ({
      ...prev,
      ...newParams,
      // Reset to page 1 when search changes
      ...(newParams.search !== undefined && newParams.search !== prev.search ? { page: 1 } : {}),
    }));
  }, []);

  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    pagination,
    isLoading,
    error,
    params,
    updateParams,
    refetch,
  };
}
