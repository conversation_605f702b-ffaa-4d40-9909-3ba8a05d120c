export interface PaginationParams {
  page: number;
  limit: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ViewState {
  view: 'cards' | 'table';
  pagination: PaginationParams;
}

export interface TableColumn<T> {
  id: string;
  header: string;
  accessorKey?: keyof T;
  cell?: (row: T) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
  width?: string;
}

export interface DataTableProps<T> {
  data: T[];
  columns: TableColumn<T>[];
  pagination: PaginatedResponse<T>['pagination'];
  onPaginationChange: (params: Partial<PaginationParams>) => void;
  isLoading?: boolean;
  searchPlaceholder?: string;
}
